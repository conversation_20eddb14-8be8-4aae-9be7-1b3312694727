/**
 * Authentication functionality has been removed
 * This file now provides dummy functions that don't perform any authentication checks
 */

// Dummy function that always returns true (user is always considered authenticated)
function isAuthenticated() {
    return true;
}

// Dummy function that doesn't redirect
function redirectToLogin() {
    console.log('Authentication has been disabled - no redirect needed');
    return;
}

// Dummy function that doesn't check authentication
function checkAuth() {
    console.log('Authentication has been disabled - no check needed');
    return;
}

// Export dummy functions for use in other scripts
export { isAuthenticated, redirectToLogin, checkAuth };
