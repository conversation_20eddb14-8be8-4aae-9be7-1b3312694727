/**
 * React-Style Google OAuth Implementation for StepStyle
 * Complete Google Sign-In functionality with modern approach
 */

class ReactGoogleOAuth {
    constructor() {
        // Updated credentials
        this.clientId = '142140028318-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com';
        this.backendUrl = 'http://localhost:5000/api/user';
        this.isInitialized = false;
        this.state = {
            isLoading: false,
            user: null,
            error: null
        };
        this.init();
    }

    /**
     * Initialize Google OAuth - React Style
     */
    async init() {
        try {
            console.log('🚀 Initializing Google OAuth...');
            
            // Wait for Google API to load
            await this.waitForGoogleAPI();
            
            // Initialize Google Sign-In with new credentials
            await google.accounts.id.initialize({
                client_id: this.clientId,
                callback: this.handleCredentialResponse.bind(this),
                auto_select: false,
                cancel_on_tap_outside: true,
                use_fedcm_for_prompt: true
            });

            this.isInitialized = true;
            console.log('✅ Google OAuth initialized successfully');
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Check if user is already logged in
            this.checkAuthState();
            
        } catch (error) {
            console.error('❌ Failed to initialize Google OAuth:', error);
            this.setState({ error: error.message });
        }
    }

    /**
     * Wait for Google API to be available
     */
    waitForGoogleAPI() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 100;
            
            const checkGoogle = () => {
                attempts++;
                if (typeof google !== 'undefined' && google.accounts && google.accounts.id) {
                    resolve();
                } else if (attempts < maxAttempts) {
                    setTimeout(checkGoogle, 100);
                } else {
                    reject(new Error('Google API failed to load after 10 seconds'));
                }
            };
            checkGoogle();
        });
    }

    /**
     * Setup event listeners for buttons
     */
    setupEventListeners() {
        // Login button
        const loginBtn = document.getElementById('google-login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.signIn();
            });
        }

        // Signup button
        const signupBtn = document.getElementById('google-signup-btn');
        if (signupBtn) {
            signupBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.signIn();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }

    /**
     * React-style state management
     */
    setState(newState) {
        this.state = { ...this.state, ...newState };
        this.updateUI();
    }

    /**
     * Update UI based on state - React style
     */
    updateUI() {
        const { isLoading, user, error } = this.state;
        
        // Update loading state
        this.updateLoadingState(isLoading);
        
        // Update user state
        if (user) {
            this.updateUserState(user);
        }
        
        // Update error state
        if (error) {
            this.showNotification(error, 'error');
        }
    }

    /**
     * Sign in with Google
     */
    async signIn() {
        if (!this.isInitialized) {
            console.error('❌ Google OAuth not initialized');
            this.showNotification('Google OAuth not ready. Please refresh the page.', 'error');
            return;
        }

        try {
            this.setState({ isLoading: true, error: null });
            
            // Trigger Google Sign-In
            google.accounts.id.prompt((notification) => {
                if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                    // Fallback to popup method
                    this.signInWithPopup();
                }
            });
            
        } catch (error) {
            console.error('❌ Sign in error:', error);
            this.setState({ isLoading: false, error: error.message });
        }
    }

    /**
     * Fallback popup sign-in method
     */
    signInWithPopup() {
        // Create temporary button for popup
        const tempDiv = document.createElement('div');
        tempDiv.style.position = 'absolute';
        tempDiv.style.top = '-9999px';
        tempDiv.style.left = '-9999px';
        document.body.appendChild(tempDiv);

        google.accounts.id.renderButton(tempDiv, {
            theme: 'outline',
            size: 'large',
            type: 'standard',
            text: 'signin_with',
            shape: 'rectangular'
        });

        // Auto-click the hidden button
        setTimeout(() => {
            const hiddenBtn = tempDiv.querySelector('div[role="button"]');
            if (hiddenBtn) {
                hiddenBtn.click();
            }
            // Cleanup
            setTimeout(() => {
                if (document.body.contains(tempDiv)) {
                    document.body.removeChild(tempDiv);
                }
            }, 1000);
        }, 100);
    }

    /**
     * Handle credential response from Google
     */
    async handleCredentialResponse(response) {
        try {
            console.log('🔑 Google credential received');
            
            this.setState({ isLoading: true, error: null });
            
            // Send token to backend for verification
            const result = await this.verifyTokenWithBackend(response.credential);
            
            if (result.success) {
                // Store authentication data
                localStorage.setItem('authToken', result.token);
                localStorage.setItem('userData', JSON.stringify(result.user));
                
                // Update state
                this.setState({ 
                    isLoading: false, 
                    user: result.user,
                    error: null 
                });
                
                // Show success message
                this.showNotification(`Welcome back, ${result.user.name}! 🎉`, 'success');
                
                // Redirect after delay
                setTimeout(() => {
                    this.redirectAfterLogin();
                }, 2000);
                
            } else {
                throw new Error(result.message || 'Authentication failed');
            }
            
        } catch (error) {
            console.error('❌ Google login error:', error);
            this.setState({ 
                isLoading: false, 
                error: `Login failed: ${error.message}` 
            });
        }
    }

    /**
     * Verify token with backend
     */
    async verifyTokenWithBackend(token) {
        try {
            console.log('🔄 Verifying token with backend...');
            
            const response = await fetch(`${this.backendUrl}/google-login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token })
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}: Backend verification failed`);
            }
            
            console.log('✅ Token verified successfully');
            return data;
            
        } catch (error) {
            console.error('❌ Backend verification error:', error);
            throw new Error(`Backend error: ${error.message}`);
        }
    }

    /**
     * Update loading state in UI
     */
    updateLoadingState(isLoading) {
        const buttons = ['google-login-btn', 'google-signup-btn'];
        
        buttons.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.disabled = isLoading;
                
                if (isLoading) {
                    btn.innerHTML = `
                        <div class="loading-spinner"></div>
                        Signing in...
                    `;
                } else {
                    // Restore original button content
                    const isLogin = btnId.includes('login');
                    btn.innerHTML = `
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" />
                        ${isLogin ? 'Login with Google' : 'Sign up with Google'}
                    `;
                }
            }
        });
    }

    /**
     * Update user state in UI
     */
    updateUserState(user) {
        // Update user icon or profile section if exists
        const userIcon = document.querySelector('.user-icon, .user-profile');
        if (userIcon && user.profilePicture) {
            userIcon.src = user.profilePicture;
            userIcon.alt = user.name;
        }
        
        // Update any user name displays
        const userNameElements = document.querySelectorAll('.user-name');
        userNameElements.forEach(el => {
            el.textContent = user.name;
        });
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        const notification = document.querySelector('.toast-notification');
        if (notification) {
            const messageEl = notification.querySelector('.toast-message');
            const iconEl = notification.querySelector('.toast-icon');
            
            if (messageEl) messageEl.textContent = message;
            
            // Update icon and color based on type
            if (iconEl) {
                iconEl.className = type === 'success' 
                    ? 'ri-check-line toast-icon' 
                    : type === 'error'
                    ? 'ri-error-warning-line toast-icon'
                    : 'ri-information-line toast-icon';
            }
            
            // Update background color
            notification.style.background = 
                type === 'success' ? '#27ae60' : 
                type === 'error' ? '#e74c3c' : '#3498db';
            
            // Show notification
            notification.classList.add('show');
            
            // Hide after 4 seconds
            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        } else {
            // Fallback to console
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Check authentication state on page load
     */
    checkAuthState() {
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');
        
        if (token && userData) {
            try {
                const user = JSON.parse(userData);
                this.setState({ user });
                console.log('✅ User already authenticated:', user.name);
            } catch (error) {
                console.error('❌ Invalid stored user data');
                this.logout();
            }
        }
    }

    /**
     * Redirect after successful login
     */
    redirectAfterLogin() {
        // Check if there's a redirect URL in session storage
        const redirectUrl = sessionStorage.getItem('redirectAfterLogin');
        
        if (redirectUrl) {
            sessionStorage.removeItem('redirectAfterLogin');
            window.location.href = redirectUrl;
        } else {
            // Default redirect to home page
            window.location.href = '/index.html';
        }
    }

    /**
     * Logout user
     */
    logout() {
        // Clear local storage
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        
        // Disable Google auto-select
        if (this.isInitialized) {
            google.accounts.id.disableAutoSelect();
        }
        
        // Update state
        this.setState({ user: null, error: null });
        
        // Show logout message
        this.showNotification('Logged out successfully! 👋', 'success');
        
        // Redirect to login page
        setTimeout(() => {
            window.location.href = '/Pages/login.html';
        }, 1500);
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!this.state.user || !!localStorage.getItem('authToken');
    }

    /**
     * Get current user data
     */
    getCurrentUser() {
        return this.state.user || (() => {
            const userData = localStorage.getItem('userData');
            return userData ? JSON.parse(userData) : null;
        })();
    }

    /**
     * Get auth token
     */
    getAuthToken() {
        return localStorage.getItem('authToken');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎯 Initializing React-style Google OAuth...');
    window.reactGoogleOAuth = new ReactGoogleOAuth();
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ReactGoogleOAuth;
}
