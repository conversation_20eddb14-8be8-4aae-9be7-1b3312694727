/* About Page Styles */
@import "../styles/style.css";

/* Color Variables */
:root {
  --primary-color: #3498db;       /* Blue */
  --secondary-color: #2ecc71;     /* Green */
  --accent-color: #9b59b6;        /* Purple */
  --dark-color: #2c3e50;          /* Dark Blue */
  --light-color: #ecf0f1;         /* Light Gray */
  --text-color: #34495e;          /* Dark Gray Blue */
  --white-color: #ffffff;         /* White */
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Toast Notification */
.toast-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--secondary-color);
  color: var(--white-color);
  padding: 15px 25px;
  border-radius: 5px;
  box-shadow: 0 4px 8px var(--shadow-color);
  display: flex;
  align-items: center;
  transform: translateY(100px);
  opacity: 0;
  /* GSAP will handle the animation */
  z-index: 1000;
}

.toast-icon {
  font-size: 20px;
  margin-right: 10px;
}

.toast-message {
  font-family: 'Gilroy-Regular', sans-serif;
  font-size: 14px;
}

/* Navigation Styles */
nav {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: transparent;
  backdrop-filter: blur(3px);
  opacity: 1;
  position: fixed;
  top: 0;
  left: 0;
  transition: top 0.3s ease-in-out;
  user-select: none;
  z-index: 20;
}

nav #logo {
  height: 7%;
  width: 4%;
  margin-left: 1rem;
  margin-right: 10rem;
}

nav #logo img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

nav #nav-middle {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  justify-content: center;
}

nav #nav-middle a {
  text-decoration: none;
  font-family: funnel display medium;
  color: black;
}

nav #nav-middle a:hover {
  color: rgba(17, 17, 17, 0.5333333333);
}

nav #nav-middle a.active {
  font-weight: 500;
}

nav #nav-last {
  display: flex;
  gap: 1.4rem;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

nav #nav-last #search-bar {
  display: flex;
  border-radius: 50px;
  border: 2px solid #111;
  width: 100%;
}

nav #nav-last #search-bar i {
  font-size: 1.3em;
}

nav #nav-last #search-bar .search-icon {
  margin-left: 0.2rem;
  margin-top: 0.3rem;
  border-radius: 50% 0 0 50%;
  background-color: transparent;
}

nav #nav-last #search-bar #nav-search {
  border: none;
  padding: 0.2rem;
  width: 10vw;
  border-radius: 0 50px 50px 0;
  font-size: 1rem;
  font-family: funnel display medium;
  outline: none;
  background-color: transparent;
  color: black;
}

nav #nav-last #search-bar #nav-search:active {
  background-color: transparent;
}

nav #nav-last #search-bar #nav-search::-webkit-search-cancel-button {
  cursor: pointer;
}

nav #nav-last .user {
  font-size: 1.7rem;
}

nav #nav-last .user:hover {
  cursor: pointer;
}

/* Hero Section */
.about-hero {
  height: 60vh;
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), var(--primary-color);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white-color);
  text-align: center;
  margin-top: 60px;
}

.hero-content {
  max-width: 800px;
  padding: 0 20px;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 20px;
  font-family: 'Bebas Neue', sans-serif;
  letter-spacing: 2px;
  /* GSAP will handle the animation, no need for initial opacity/transform */
}

.hero-content p {
  font-size: 1.5rem;
  font-family: 'Helvetica Now Display', sans-serif;
  /* GSAP will handle the animation, no need for initial opacity/transform */
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  font-family: 'helvetica now display';
}

/* Brand Story Section */
.brand-story {
  padding: 80px 0;
  background-color: #fff;
}

.brand-story .container {
  display: flex;
  align-items: center;
  gap: 50px;
}

.story-content {
  flex: 1;
  /* GSAP will handle the animation */
}

.story-content h2 {
  font-size: 2.5rem;
  margin-bottom: 30px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.story-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  font-family: 'Gilroy-Regular', sans-serif;
}

.story-image {
  flex: 1;
  /* GSAP will handle the animation */
}

.story-image img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Mission & Values Section */
.mission-values {
  padding: 80px 0;
  background-color: var(--light-color);
}

.mission-values .container {
  display: flex;
  gap: 50px;
}

.mission-content {
  flex: 1;
}

.mission-content h2 {
  font-size: 2.5rem;
  margin-bottom: 30px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.mission-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  font-family: 'Gilroy-Regular', sans-serif;
}

.values-content {
  flex: 1;
}

.values-content h2 {
  font-size: 2.5rem;
  margin-bottom: 30px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.value-item {
  background-color: var(--white-color);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  /* GSAP will handle the animation */
  border-top: 4px solid var(--primary-color);
}

.value-item:nth-child(2) {
  border-top-color: var(--secondary-color);
}

.value-item:nth-child(3) {
  border-top-color: var(--accent-color);
}

.value-item:nth-child(4) {
  border-top-color: var(--dark-color);
}

.value-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.value-item i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.value-item:nth-child(2) i {
  color: var(--secondary-color);
}

.value-item:nth-child(3) i {
  color: var(--accent-color);
}

.value-item:nth-child(4) i {
  color: var(--dark-color);
}

.value-item h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.value-item p {
  font-size: 1rem;
  line-height: 1.5;
  font-family: 'Gilroy-Regular', sans-serif;
}

/* Design Philosophy Section */
.design-philosophy {
  padding: 80px 0;
  background-color: #fff;
}

.design-philosophy .container {
  display: flex;
  align-items: center;
  gap: 50px;
}

.philosophy-image {
  flex: 1;
}

.philosophy-image img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.philosophy-content {
  flex: 1;
}

.philosophy-content h2 {
  font-size: 2.5rem;
  margin-bottom: 30px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.philosophy-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  font-family: 'Gilroy-Regular', sans-serif;
}

/* Founders Section */
.founders-section {
  padding: 100px 0;
  background-color: var(--white-color);
  position: relative;
  overflow: hidden;
}

.founders-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(155, 89, 182, 0.05) 100%);
  z-index: 0;
}

.founders-section .container {
  position: relative;
  z-index: 1;
}

.founders-section h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: var(--dark-color);
}

.founders-intro {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 4rem;
  font-size: 1.2rem;
  color: var(--text-color);
}

.founders-story {
  display: flex;
  gap: 4rem;
  margin-bottom: 5rem;
  align-items: center;
}

@media (max-width: 992px) {
  .founders-story {
    flex-direction: column;
    gap: 2rem;
  }
}

.founders-image {
  flex: 1;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
}

.founders-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.founders-image:hover img {
  transform: scale(1.03);
}

.founders-content {
  flex: 1;
}

.founders-content h3 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  position: relative;
  display: inline-block;
}

.founders-content h3::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 3px;
}

.founders-content p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: var(--text-color);
}

.founders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
}

.founder-card {
  background-color: var(--white-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.founder-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.founder-image {
  height: 300px;
  overflow: hidden;
}

.founder-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: top;
  transition: transform 0.5s ease;
}

.founder-card:hover .founder-image img {
  transform: scale(1.05);
}

.founder-info {
  padding: 2rem;
  position: relative;
  font-family: 'Helvetica Now Display', sans-serif;
}

.founder-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 5px;
  background: var(--primary-color);
  border-radius: 5px;
}

.founder-card:nth-child(2) .founder-info::before {
  background: var(--secondary-color);
}

.founder-card:nth-child(3) .founder-info::before {
  background: var(--accent-color);
}

.founder-info h3 {
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  color: var(--dark-color);
}

.founder-title {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.founder-card:nth-child(2) .founder-title {
  color: var(--secondary-color);
}

.founder-card:nth-child(3) .founder-title {
  color: var(--accent-color);
}

.founder-quote {
  font-style: italic;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  color: var(--text-color);
  position: relative;
  padding-left: 1.5rem;
}

.founder-quote::before {
  content: '"';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 2.5rem;
  line-height: 1;
  color: rgba(0, 0, 0, 0.1);
}

.founder-social {
  display: flex;
  gap: 1rem;
}

.founder-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--light-color);
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.founder-card:nth-child(2) .founder-social a {
  color: var(--secondary-color);
}

.founder-card:nth-child(3) .founder-social a {
  color: var(--accent-color);
}

.founder-social a:hover {
  background-color: var(--primary-color);
  color: var(--white-color);
  transform: translateY(-3px);
}

.founder-card:nth-child(2) .founder-social a:hover {
  background-color: var(--secondary-color);
}

.founder-card:nth-child(3) .founder-social a:hover {
  background-color: var(--accent-color);
}

.founder-social i {
  font-size: 1.2rem;
}

/* Team Section */
.team-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
  text-align: center;
  color: var(--white-color);
}

.team-section h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.team-intro {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto 50px;
  font-family: 'Gilroy-Regular', sans-serif;
}

.team-grid {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.team-member {
  flex: 1;
  max-width: 300px;
  background-color: var(--white-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  /* GSAP will handle the animation */
  position: relative;
}

.team-member:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.team-member::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--primary-color);
  transition: height 0.3s ease;
}

.team-member:nth-child(2)::before {
  background: var(--secondary-color);
}

.team-member:nth-child(3)::before {
  background: var(--accent-color);
}

.team-member:hover::before {
  height: 10px;
}

.team-member img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.team-member h3 {
  font-size: 1.3rem;
  margin: 20px 0 5px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.team-member p {
  font-size: 1rem;
  color: #666;
  margin-bottom: 20px;
  font-family: 'Gilroy-Regular', sans-serif;
}

/* Join Us Section */
.join-us {
  padding: 80px 0;
  background: linear-gradient(45deg, var(--accent-color) 0%, var(--primary-color) 100%);
  background-size: cover;
  background-position: center;
  color: var(--white-color);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.join-us::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect fill="none" width="100" height="100"/><rect fill="rgba(255,255,255,0.05)" x="25" y="25" width="50" height="50" transform="rotate(45 50 50)"/></svg>');
  background-size: 60px 60px;
  z-index: 0;
  opacity: 0.5;
}

.join-us .container {
  position: relative;
  z-index: 1;
}

.join-us h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-family: 'Helvetica Now Display', sans-serif;
  font-weight: 600;
}

.join-us p {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto 40px;
  font-family: 'Gilroy-Regular', sans-serif;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-family: 'Helvetica Now Display', sans-serif;
}

.primary-btn {
  background-color: var(--secondary-color);
  color: var(--white-color);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  z-index: -1;
}

.primary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.primary-btn:hover::before {
  width: 100%;
}

.secondary-btn {
  background-color: transparent;
  color: var(--white-color);
  border: 2px solid var(--white-color);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.secondary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--white-color);
  transition: width 0.3s ease;
  z-index: -1;
}

.secondary-btn:hover {
  color: var(--accent-color);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.secondary-btn:hover::before {
  width: 100%;
}

/* Footer Styles */
.modern-footer {
  background-color: var(--dark-color);
  color: var(--white-color);
  padding: 60px 0 30px;
  margin-top: 0;
  position: relative;
}

.modern-footer .footer-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 40px;
}

.modern-footer .footer-container .footer-column {
  flex: 1;
  min-width: 200px;
}

.modern-footer .footer-container .footer-column h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 600;
  position: relative;
  display: inline-block;
  font-family: 'Helvetica Now Display', sans-serif;
}

.modern-footer .footer-container .footer-column h3:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
}

.modern-footer .footer-container .footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.modern-footer .footer-container .footer-column ul li {
  margin-bottom: 10px;
}

.modern-footer .footer-container .footer-column ul li a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.95rem;
  font-family: 'Gilroy-Regular', sans-serif;
}

.modern-footer .footer-container .footer-column ul li a:hover {
  color: var(--primary-color);
}

.modern-footer .footer-container .footer-column .social-icons {
  display: flex;
  gap: 25px;
  margin-bottom: 25px;
}

.modern-footer .footer-container .footer-column .social-icons .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--white-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

.modern-footer .footer-container .footer-column .social-icons .social-icon:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.modern-footer .footer-container .footer-column .social-icons .social-icon i {
  font-size: 1.2rem;
}

.modern-footer .footer-container .footer-column .newsletter h4 {
  font-size: 1rem;
  margin-bottom: 15px;
  font-family: 'Helvetica Now Display', sans-serif;
}

.modern-footer .footer-container .footer-column .newsletter .newsletter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.modern-footer .footer-container .footer-column .newsletter .newsletter-form input {
  flex: 1;
  min-width: 150px;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white-color);
  font-family: 'Gilroy-Regular', sans-serif;
}

.modern-footer .footer-container .footer-column .newsletter .newsletter-form input::placeholder {
  color: #aaa;
}

.modern-footer .footer-container .footer-column .newsletter .newsletter-form button {
  padding: 10px 15px;
  background-color: var(--primary-color);
  color: var(--white-color);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Helvetica Now Display', sans-serif;
}

.modern-footer .footer-container .footer-column .newsletter .newsletter-form button:hover {
  background-color: var(--secondary-color);
}

.modern-footer .footer-bottom {
  max-width: 1200px;
  margin: 40px auto 0;
  padding: 20px 20px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.modern-footer .footer-bottom .copyright {
  font-size: 0.9rem;
  color: #aaa;
  font-family: 'Gilroy-Regular', sans-serif;
}

.modern-footer .footer-bottom .footer-links {
  display: flex;
  gap: 20px;
}

.modern-footer .footer-bottom .footer-links a {
  color: #aaa;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  font-family: 'Gilroy-Regular', sans-serif;
}

.modern-footer .footer-bottom .footer-links a:hover {
  color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .brand-story .container,
  .mission-values .container,
  .design-philosophy .container {
    flex-direction: column;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    flex-wrap: wrap;
  }

  .team-member {
    min-width: 250px;
  }

  .modern-footer .footer-container {
    flex-direction: column;
    gap: 30px;
  }

  .modern-footer .footer-container .footer-column {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 3rem;
  }

  .hero-content p {
    font-size: 1.2rem;
  }

  .story-content h2,
  .mission-content h2,
  .values-content h2,
  .philosophy-content h2,
  .team-section h2,
  .join-us h2 {
    font-size: 2rem;
  }

  .modern-footer .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .modern-footer .footer-bottom .footer-links {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .cta-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .btn {
    width: 100%;
  }

  .modern-footer {
    padding: 40px 0 20px;
  }

  .modern-footer .footer-container {
    gap: 25px;
  }

  .modern-footer .footer-container .footer-column h3 {
    font-size: 1.1rem;
  }

  .modern-footer .footer-container .footer-column ul li a,
  .modern-footer .footer-bottom .copyright,
  .modern-footer .footer-bottom .footer-links a {
    font-size: 0.85rem;
  }

  .modern-footer .footer-bottom .footer-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
}