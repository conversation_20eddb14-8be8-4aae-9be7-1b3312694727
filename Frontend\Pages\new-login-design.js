// Modern Login Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Form switching functionality
    const switchLinks = document.querySelectorAll('.switch-link');
    const formContents = document.querySelectorAll('.form-content');

    switchLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('data-target');
            
            console.log('Switching to:', target); // Debug log
            
            // Hide all forms
            formContents.forEach(form => {
                form.classList.remove('active');
            });
            
            // Show target form
            const targetForm = document.getElementById(target + '-form');
            if (targetForm) {
                targetForm.classList.add('active');
                console.log('Form switched successfully'); // Debug log
            } else {
                console.error('Target form not found:', target + '-form');
            }
        });
    });

    // Password toggle functionality
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const passwordInput = this.parentElement.querySelector('input[type="password"], input[type="text"]');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'ri-eye-off-line';
            } else {
                passwordInput.type = 'password';
                icon.className = 'ri-eye-line';
            }
        });
    });

    // Login form submission
    const loginForm = document.getElementById('login-form-element');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            // Basic validation
            if (!email || !password) {
                showToast('Please fill in all fields', 'error');
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Logging in...';
            submitBtn.disabled = true;
            
            // Simulate login (replace with actual API call)
            setTimeout(() => {
                // Reset button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                // Show success message
                showToast('Login successful! Redirecting...', 'success');
                
                // Redirect to home page after delay
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1500);
            }, 2000);
        });
    }

    // Register form submission
    const registerForm = document.getElementById('register-form-element');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const terms = document.getElementById('terms').checked;
            
            // Basic validation
            if (!name || !email || !password) {
                showToast('Please fill in all fields', 'error');
                return;
            }
            
            if (!terms) {
                showToast('Please agree to the Terms & Conditions', 'error');
                return;
            }
            
            if (password.length < 6) {
                showToast('Password must be at least 6 characters long', 'error');
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Creating Account...';
            submitBtn.disabled = true;
            
            // Simulate registration (replace with actual API call)
            setTimeout(() => {
                // Reset button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                // Show success message
                showToast('Account created successfully! Redirecting...', 'success');
                
                // Redirect to home page after delay
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1500);
            }, 2000);
        });
    }

    // Google login functionality
    const googleLoginBtn = document.getElementById('google-login-btn');
    const googleSignupBtn = document.getElementById('google-signup-btn');

    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', function() {
            console.log('Google Login button clicked');
            if (window.googleAuth) {
                window.googleAuth.signIn();
            } else {
                showToast('Google Sign-In not available. Please refresh the page.', 'error');
            }
        });
    }

    if (googleSignupBtn) {
        googleSignupBtn.addEventListener('click', function() {
            console.log('Google Signup button clicked');
            if (window.googleAuth) {
                window.googleAuth.signIn();
            } else {
                showToast('Google Sign-In not available. Please refresh the page.', 'error');
            }
        });
    }

    // Forgot password functionality
    const forgotPasswordLinks = document.querySelectorAll('.forgot-password');
    forgotPasswordLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            showToast('Forgot password feature coming soon!', 'info');
        });
    });

    // Toast notification function
    function showToast(message, type = 'success') {
        const toast = document.querySelector('.toast-notification');
        const toastMessage = toast.querySelector('.toast-message');
        const toastIcon = toast.querySelector('.toast-icon');
        
        // Set message
        toastMessage.textContent = message;
        
        // Set icon and color based on type
        switch(type) {
            case 'success':
                toastIcon.className = 'ri-check-line toast-icon';
                toast.style.background = '#27ae60';
                break;
            case 'error':
                toastIcon.className = 'ri-close-line toast-icon';
                toast.style.background = '#e74c3c';
                break;
            case 'info':
                toastIcon.className = 'ri-information-line toast-icon';
                toast.style.background = '#3498db';
                break;
        }
        
        // Show toast
        toast.classList.add('show');
        
        // Hide toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // Check URL parameters for form switching
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    
    if (tab === 'register') {
        // Hide all forms
        formContents.forEach(form => {
            form.classList.remove('active');
        });
        
        // Show register form
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.classList.add('active');
        }
    }

    // Add smooth animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    // Observe form elements for animation
    const formElements = document.querySelectorAll('.form-group, .google-btn, .login-btn, .create-btn');
    formElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });

    // Trigger animations after a short delay
    setTimeout(() => {
        formElements.forEach((el, index) => {
            setTimeout(() => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }, 300);

    // Debug: Log current active form
    console.log('Current active form:', document.querySelector('.form-content.active')?.id);
});

// Add some interactive effects
document.addEventListener('mousemove', function(e) {
    const leftSide = document.querySelector('.left-side');
    if (leftSide) {
        const rect = leftSide.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 20;
        const rotateY = (centerX - x) / 20;
        
        const shoeImage = leftSide.querySelector('.shoe-image img');
        if (shoeImage) {
            shoeImage.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        }
    }
});

// Reset shoe image on mouse leave
document.querySelector('.left-side')?.addEventListener('mouseleave', function() {
    const shoeImage = this.querySelector('.shoe-image img');
    if (shoeImage) {
        shoeImage.style.transform = 'rotateX(0deg) rotateY(0deg)';
    }
});
