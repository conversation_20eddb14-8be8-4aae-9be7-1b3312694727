@font-face {
    font-family: "<PERSON>roy-Regular";
    src: url('./assets/fonts/<PERSON>roy-Regular.ttf');
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    width: 100%;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
    position: relative;
    width: 100%;

    header {
        height: 109vh;
        width: 100%;
        overflow: hidden;

        #cursor {
            height: 2.8%;
            width: 1.5%;
            background-color: #0a0a0a;
            border-radius: 50%;
            position: fixed;
            z-index: 9;
            box-shadow: 0px 0px 10px 1px #000000;
            display: none;
        }

        nav {
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background-color: transparent;
            backdrop-filter: blur(3px);
            opacity: 1;
            position: fixed;
            top: 0;
            left: 0;
            transition: top 0.3s ease-in-out;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
            z-index: 20;

            #logo {
                height: 7%;
                width: 4%;
                margin-left: 1rem;
                margin-right: 10rem;

                img {
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                }
            }

            #nav-middle {
                display: flex;
                align-items: center;
                gap: 2.5rem;
                justify-content: center;

                a {
                    text-decoration: none;
                    font-family: funnel display medium;
                    color: black;
                }

                a:hover {
                    color: #11111188;
                }
            }

            #nav-last {
                display: flex;
                gap: 1.4rem;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;

                #search-bar {
                    display: flex;
                    border-radius: 50px;
                    border: 2px solid #111;
                    width: 100%;

                    i {
                        font-size: 1.3em;
                    }

                    .search-icon {
                        margin-left: .2rem;
                        margin-top: .3rem;
                        border-radius: 50% 0 0 50%;
                        background-color: transparent;
                    }

                    #nav-search {
                        border: none;
                        padding: .2rem;
                        width: 10vw;
                        border-radius: 0 50px 50px 0;
                        font-size: 1rem;
                        font-family: funnel display medium;
                        outline: none;
                        background-color: transparent;
                        color: black;
                    }

                    #nav-search:active {
                        background-color: transparent;
                    }

                    #nav-search::-webkit-search-cancel-button {
                        cursor: pointer;
                    }
                }

                .cart {
                    font-size: 1.5rem;
                    padding: 5px;
                    border-radius: 50%;
                }

                .cart:hover,
                .cart:active {
                    cursor: pointer;
                }

                .user {
                    font-size: 1.7rem;
                }

                .user:hover {
                    cursor: pointer;
                }
            }
        }

        #page1 {
            width: 100%;
            height: 150vh;
            z-index: 1;
            background-color: #fff;

            #page1-part1 {
                display: flex;
                align-items: center;
                justify-content: center;
                transform: translate(0, -1%);
                position: relative;
                user-select: none;

                #bgcolor {
                    height: 80%;
                    width: 38%;
                    background: linear-gradient(120deg, #fcce80, #FAA484);
                    transform: translate(0, 10%);
                    filter: blur(70px);
                    border-radius: 50%;
                    position: absolute;
                }

                h1 {
                    font-family: 'Bebas Neue';
                    color: #000;
                    font-size: 20rem;
                    font-weight: 100;
                    text-transform: uppercase;
                    margin-top: 10rem;
                    margin-left: 2rem;
                    z-index: 9;

                    span {
                        display: inline-block;
                    }
                }

                img {
                    height: 70%;
                    width: 50%;
                    object-fit: cover;
                    position: absolute;
                    transform: translate(-12%, -3%);
                    z-index: 10;
                    rotate: -30deg;
                }
            }
        }
    }

    #page2 {
        width: 100%;
        height: 100vh;
        position: relative;
        display: flex;
        flex-direction: row;
        background-color: #fff;
        gap: 1rem;

        .promo {
            display: flex;
            position: relative;
            width: 25%;
            height: 90%;
            gap: .5rem;

            #developing {
                display: flex;
                align-items: center;
                justify-content: center;
                background-size: 2rem;
                width: 100%;
                height: 80%;
                transform: translate(.1%, 19%);
                background-color: #fff;
            }

            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                position: absolute;
                z-index: 9;
            }
            #airmax{
                height: 80%;
                width: 70%;
                z-index: 9;
            }
            #AirMax1000 {
                height: 100%;
                width: 100%;
                object-fit: cover;
                z-index: 10;
            }

            .mouse-video {
                position: absolute;
                width: 100%;
                height: 100%;
                overflow: hidden;
                // border-radius: 8px;
                pointer-events: none;
                opacity: 0;
                transform: scale(0.8);
                transition: transform 0.3s ease, opacity 0.3s ease;
                z-index: 10;
                /* Center the video over the image */
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0.8);

                video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .mouse-video.active {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }

        }

    }

    #page3 {
        width: 100%;
        height: 120vh;
        overflow-y: hidden;
        z-index: 1;

        .trending-part {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 2rem;

            h1 {
                font-family: Helvetica now display medium;
                font-size: 1.5rem;
                font-weight: 570;
                margin-bottom: 1.5rem;
                letter-spacing: -.9px;
                margin-left: 2rem;
            }
        }

        .mySwiper {
            width: 100%;
            height: 100%;

            .swiper-slide {
                text-align: center;
                margin-left: 2rem;
                font-size: 18px;
                background: #fff;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: left;
                width: 29%;
                height: 70%;
                user-select: none;
                gap: 1rem;

                .card-upper {
                    height: 97%;
                    width: 90%;

                    img {
                        height: 100%;
                        width: 100%;
                        object-fit: cover;
                    }
                }

                .card-lower {
                    display: flex;
                    align-items: left;
                    text-align: left;
                    flex-direction: column;
                    margin-top: 1.3rem;

                    h3 {
                        font-family: helvetica now display medium;
                        font-size: 1.1rem;
                        font-weight: 100;
                        letter-spacing: .01rem;
                        // word-spacing: rem;
                    }

                    .gender {
                        font-size: 1.1rem;
                        color: rgba(66, 66, 66, 0.87);
                    }

                    .price {
                        font-size: 1.1rem;
                    }

                }
            }

            .swiper-slide:hover {
                cursor: pointer;
            }
        }
    }

    #page4 {
        height: 140vh;
        width: 100%;
        display: flex;
        // align-items: center;
        // justify-content: center;
        flex-direction: column;

        h1 {
            font-family: Helvetica now display medium;
            font-size: 1.5rem;
            font-weight: 570;
            margin-bottom: 1.5rem;
            letter-spacing: -.9px;
            margin-left: 2rem;
        }

        .page4-grid {
            display: grid;
            margin-left: 1rem;
            height: 50%;
            grid-template-columns: repeat(4, 2fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1rem;
            width: 95%;
            grid-template-areas:
                "a a a a"
                "b b c c"

            ;

            img {
                width: 100%;
                aspect-ratio: 3/1;
                object-fit: cover;
            }

            .image-a {
                grid-area: a;
                object-position: 74% 74%;
                height: 100%;
                position: relative;

                img {
                    height: 100%;
                    width: 100%;
                    object-position: 74% 74%;
                }

                #image-a-content {
                    // position: absolute;
                    // top: 40%;\
                    display: flex;
                    flex-direction: column;
                    transform: translate(2%, -37vw);
                    font-family: Gilroy-Regular;
                    color: #fff;

                    h4 {
                        font-weight: 100;
                        font-size: 1rem;
                        padding-left: 2rem;
                    }

                    h1 {
                        font-weight: 900;
                        font-family: gilroy;
                        letter-spacing: .1rem;
                        font-size: 2.5rem;
                        text-transform: uppercase;
                    }

                    p {
                        font-weight: 100;
                        font-size: 1rem;
                        padding-left: 2rem;
                        width: 24%;
                    }

                    button {
                        width: 6%;
                        padding: .5rem 1rem;
                        margin-left: 2rem;
                        margin-top: 1.5rem;
                        border-radius: .2rem;
                        border: none;
                        background-color: #111;
                        color: #fff;
                        font-family: helvetica now display medium;
                    }

                    button:hover {
                        cursor: pointer;
                    }
                }
            }

            .image-b {
                grid-area: b;
                height: 100%;

                img {
                    height: 100%;
                    width: 100%;
                    // object-position: 74% 74%;
                }

                #image-b-content {
                    display: flex;
                    flex-direction: column;
                    transform: translate(6%, -4.5vw);
                    line-height: 1.5rem;
                    color: #fff;

                    h3 {
                        font-family: gilroy-regular;
                        font-weight: 600;
                    }
                }
            }

            .image-b:hover {
                cursor: pointer;
            }

            .image-c {
                grid-area: c;
                height: 100%;

                img {
                    height: 100%;
                    width: 100%;
                }

                #image-c-content {
                    display: flex;
                    flex-direction: column;
                    transform: translate(5vh, -3vw);
                    color: #fff;

                    h3 {
                        font-family: gilroy-regular;
                        font-weight: 600;
                    }
                }
            }

            .image-c:hover {
                cursor: pointer;
            }
        }
    }

    #page5 {
        width: 100%;
        height: 100vh;

        #page5-bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-left: 2rem;
            font-family: gilroy-regular;

            #page5-first-bottom {
                display: flex;
                flex-direction: column;

                h4 {
                    font-family: helvetica now display medium;
                    font-weight: 100;
                    color: #000000dc;
                    font-size: 1.2rem;
                }

                h1 {
                    font-family: gilroy;
                    font-size: 4rem;
                    text-transform: uppercase;
                    width: 80%;
                }

                p {
                    font-family: helvetica now display medium;
                    width: 49%;
                    color: #000000dc;
                }

                button {
                    margin-top: 2rem;
                    padding: .5rem .5rem;
                    border: none;
                    background-color: #111;
                    color: #fff;
                    font-family: helvetica now display medium;
                    border-radius: .3rem;
                    width: 16%;
                }

                button:hover {
                    cursor: not-allowed;
                    background-color: #000000ab;
                }
            }

            #page5-last-bottom {
                #page5-image {
                    display: flex;
                    margin-right: 4rem;
                    height: 100%;
                    width: 100%;

                    img {
                        height: 50%;
                        width: 50%;
                        object-fit: cover;
                    }

                    #desgin {
                        height: 50%;
                        width: 20%;
                        background-color: rgb(255, 255, 80);
                        position: absolute;
                    }
                }
            }
        }

        .modern-footer {
            background-color: #000000;
            color: #ffffff;
            padding: 60px 0 0;
            font-family: 'Helvetica Now Display', Arial, sans-serif;

            .footer-container {
                max-width: 1200px;
                margin: 0 auto;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                padding: 0 20px 40px;

                .footer-column {
                    width: 22%;
                    margin-bottom: 30px;

                    @media (max-width: 992px) {
                        width: 45%;
                    }

                    @media (max-width: 576px) {
                        width: 100%;
                    }

                    h3 {
                        color: #ffffff;
                        font-size: 18px;
                        font-weight: 600;
                        margin-bottom: 20px;
                        position: relative;
                        padding-bottom: 10px;

                        &:after {
                            content: '';
                            position: absolute;
                            left: 0;
                            bottom: 0;
                            width: 30px;
                            height: 2px;
                            background-color: #ffffff;
                        }
                    }

                    ul {
                        list-style: none;
                        padding: 0;
                        margin: 0;

                        li {
                            margin-bottom: 10px;

                            a {
                                color: #cccccc;
                                text-decoration: none;
                                font-size: 14px;
                                transition: color 0.3s ease;

                                &:hover {
                                    color: #ffffff;
                                }
                            }
                        }
                    }

                    .social-icons {
                        display: flex;
                        margin-bottom: 30px;

                        .social-icon {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 36px;
                            height: 36px;
                            background-color: #333333;
                            border-radius: 50%;
                            margin-right: 10px;
                            color: #ffffff;
                            transition: background-color 0.3s ease;

                            &:hover {
                                background-color: #ff5a5f;
                            }

                            i {
                                font-size: 16px;
                            }
                        }
                    }

                    .newsletter {
                        h4 {
                            font-size: 16px;
                            margin-bottom: 15px;
                            font-weight: 400;
                        }

                        .newsletter-form {
                            display: flex;

                            input {
                                flex: 1;
                                background-color: #333333;
                                border: none;
                                padding: 10px 15px;
                                color: #ffffff;
                                font-size: 14px;
                                border-radius: 4px 0 0 4px;

                                &::placeholder {
                                    color: #999999;
                                }
                            }

                            button {
                                background-color: #ff5a5f;
                                color: #ffffff;
                                border: none;
                                padding: 10px 15px;
                                font-size: 14px;
                                cursor: pointer;
                                border-radius: 0 4px 4px 0;
                                transition: background-color 0.3s ease;

                                &:hover {
                                    background-color: #ff3c41;
                                }
                            }
                        }
                    }
                }
            }

            .footer-bottom {
                border-top: 1px solid #333333;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                max-width: 1200px;
                margin: 0 auto;

                @media (max-width: 768px) {
                    flex-direction: column;
                    text-align: center;
                }

                .copyright {
                    color: #999999;
                    font-size: 14px;
                    margin: 0;

                    @media (max-width: 768px) {
                        margin-bottom: 15px;
                    }
                }

                .footer-links {
                    display: flex;

                    @media (max-width: 576px) {
                        flex-direction: column;
                        align-items: center;
                    }

                    a {
                        color: #999999;
                        text-decoration: none;
                        font-size: 14px;
                        margin-left: 20px;
                        transition: color 0.3s ease;

                        @media (max-width: 576px) {
                            margin: 5px 0;
                        }

                        &:hover {
                            color: #ffffff;
                        }
                    }
                }
            }
        }

    }
}

.no-scroll {
    overflow: hidden;
}

main::-moz-selection {
    background-color: black;
    color: #fff;
}

main::selection {
    background-color: rgb(0, 0, 0);
    color: #fff;
}

#loader {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    position: fixed;
    overflow: hidden;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000000;
    z-index: 99;

    h3 {
        color: #fff;
        font-family: Bebas Neue;
        font-size: 8rem;
        // z-index: ;
    }

    img {
        height: 10%;
        width: 15%;
        transform: translate(15%, -3%);
        position: absolute;
        object-fit: cover;
    }
}

/* Cart Icon Container */
.cart-icon-container {
    position: relative;
    display: inline-block;
    cursor: pointer;

    .cart-count {
        position: absolute;
        top: -8px;
        right: -8px;
        background-color: #ff4d4d;
        color: white;
        font-size: 0.7rem;
        min-width: 18px;
        height: 18px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        transition: transform 0.3s ease;
        padding: 2px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        z-index: 10;
        font-family: 'Helvetica Now Display', Arial, sans-serif;
    }

    &.animate .cart-count {
        animation: pulse 0.5s ease;
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* Cart Modal Styles */
.cart-modal {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100%;
    background-color: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow-y: auto;

    &::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: -1;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    &.open {
        right: 0;

        &::before {
            opacity: 1;
            visibility: visible;
        }
    }

    .cart-modal-content {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: white;
        border-radius: 10px 0 0 10px;
    }

    .cart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 15px;
        border-bottom: 2px solid #333;
        margin-bottom: 20px;
        position: relative;

        h3 {
            font-size: 1.5rem;
            margin: 0;
            font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
            font-weight: 600;
            position: relative;
            padding-left: 30px;

            &::before {
                content: '🛒';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                font-size: 1.2rem;
            }
        }

        .close-cart-btn {
            background: none;
            border: none;
            font-size: 1.8rem;
            cursor: pointer;
            color: #333;
            transition: all 0.3s ease;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            &:hover {
                color: #ff4d4d;
                background-color: rgba(0, 0, 0, 0.05);
                transform: rotate(90deg);
            }
        }
    }

    .cart-items {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 20px;

        .cart-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ddd;
            transition: all 0.3s ease;

            &:hover {
                background-color: rgba(0, 0, 0, 0.02);
            }

            .cart-item-image {
                width: 80px;
                height: 80px;
                border-radius: 8px;
                overflow: hidden;
                margin-right: 15px;
                background-color: #f8f8f8;
                padding: 5px;
                border: 1px solid #ddd;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    transition: transform 0.3s ease;
                }

                &:hover img {
                    transform: scale(1.1);
                }
            }

            .cart-item-details {
                flex: 1;

                .cart-item-name {
                    font-weight: 600;
                    margin-bottom: 5px;
                    font-size: 1rem;
                }

                .cart-item-price {
                    color: #333;
                    font-weight: 600;
                    margin-bottom: 12px;
                }

                .cart-item-controls {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .quantity-control {
                        display: flex;
                        align-items: center;
                        background-color: #f5f5f5;
                        border-radius: 20px;
                        padding: 2px;
                        border: 1px solid #ddd;

                        button {
                            width: 28px;
                            height: 28px;
                            border-radius: 50%;
                            background-color: white;
                            border: 1px solid #ddd;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 1.1rem;
                            font-weight: bold;
                            transition: all 0.3s ease;
                            cursor: pointer;
                            color: #333;

                            &:hover {
                                background-color: #333;
                                color: white;
                                transform: scale(1.05);
                            }

                            &:active {
                                transform: scale(0.95);
                            }
                        }

                        .quantity {
                            font-weight: 600;
                            width: 30px;
                            text-align: center;
                            font-size: 0.95rem;
                        }
                    }

                    .remove-item {
                        background-color: transparent;
                        border: none;
                        color: #666;
                        cursor: pointer;
                        font-size: 0.85rem;
                        transition: all 0.3s ease;
                        padding: 5px 10px;
                        border-radius: 15px;
                        border: 1px solid transparent;

                        &:hover {
                            color: #ff4d4d;
                            border-color: #ff4d4d;
                        }

                        &:active {
                            transform: scale(0.95);
                        }
                    }
                }
            }
        }

        .empty-cart-message {
            text-align: center;
            padding: 50px 0;
            color: #666;
            font-size: 1.1rem;
            font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
            position: relative;

            &::before {
                content: '🛒';
                display: block;
                font-size: 3rem;
                margin-bottom: 15px;
                opacity: 0.5;
            }

            .empty-cart-divider {
                margin: 20px auto;
                width: 50px;
                height: 2px;
                background-color: #ddd;
            }
        }
    }

    .cart-footer {
        padding-top: 15px;
        border-top: 2px solid #333;
        background-color: #f9f9f9;
        padding: 20px;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.05);

        .cart-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 1.2rem;
            font-weight: 600;
            font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;

            span:first-child {
                color: #333;
            }

            .total-amount {
                color: #333;
                font-size: 1.4rem;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -5px;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background-color: #ff4d4d;
                    transform: scaleX(0);
                    transition: transform 0.3s ease;
                    transform-origin: right;
                }

                &:hover::after {
                    transform: scaleX(1);
                    transform-origin: left;
                }
            }
        }

        .checkout-btn {
            width: 100%;
            padding: 15px;
            background-color: #333;
            color: white;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            letter-spacing: 0.5px;
            font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;

            &::after {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: -100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: all 0.5s ease;
            }

            &:hover {
                background-color: #ff4d4d;
                transform: translateY(-3px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);

                &::after {
                    left: 100%;
                }
            }

            &:active {
                transform: translateY(0);
            }
        }
    }
}

/* Toast Notification Styles */
.toast-notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: white;
    color: #333;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    z-index: 1001;
    transform: translateY(100px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    font-family: 'Helvetica Now Display', Arial, sans-serif;

    &.show {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    &.success {
        border-left: 4px solid #4CAF50;

        .toast-icon {
            color: #4CAF50;
        }
    }

    &.error {
        border-left: 4px solid #F44336;

        .toast-icon {
            color: #F44336;
        }
    }

    .toast-content {
        display: flex;
        align-items: center;

        .toast-icon {
            font-size: 1.5rem;
            margin-right: 10px;
        }

        .toast-message {
            font-size: 0.95rem;
            font-weight: 500;
        }
    }
}