/* Modern Login & Registration Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Helvetica+Now+Display:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #e74c3c;
    --secondary-color: #2c3e50;
    --accent-color: #ff6464;
    --success-color: #2ecc71;
    --error-color: #e74c3c;
    --text-color: #333;
    --light-gray: #f8f8f8;
    --medium-gray: #e0e0e0;
    --dark-gray: #666;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

html, body {
    height: 100%;
    width: 100%;
    font-family: 'Inter', 'Helvetica Now Display', sans-serif;
    background-color: #f5f5f5;
}

.modern-login {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, #f5f5f5, #e9e9e9);
}

.login-container {
    width: 100%;
    max-width: 1200px;
    height: 650px;
    display: flex;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Left side with image */
.login-image-side {
    flex: 1;
    background-image: url('../../../assets/images/nike-airForce.png');
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 30px;
    color: white;
}

.login-image-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.7) 100%);
    z-index: 1;
}

.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.selected-works {
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 1px;
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

.sign-up-btn, .join-us-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.sign-up-btn:hover, .join-us-btn:hover, .sign-up-btn.active, .join-us-btn.active {
    background: rgba(255, 255, 255, 0.4);
}

.profile-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.profile-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid white;
}

.profile-text h3 {
    font-size: 18px;
    margin-bottom: 5px;
}

.profile-text p {
    font-size: 14px;
    opacity: 0.8;
}

.profile-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.4);
}

/* Right side with forms */
.login-form-side {
    flex: 1;
    background-color: white;
    padding: 40px;
    display: flex;
    flex-direction: column;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 60px;
}

.brand-name {
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 2px;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: var(--dark-gray);
    cursor: pointer;
}

.form-content {
    width: 100%;
    max-width: 450px;
    display: none;
    margin: 0 auto;
}

.form-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.form-content h2 {
    font-size: 32px;
    margin-bottom: 10px;
    color: var(--text-color);
    font-weight: 700;
}

.form-subtitle {
    color: var(--dark-gray);
    margin-bottom: 40px;
    font-size: 16px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
    outline: none;
}

.password-input-container {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
    cursor: pointer;
}

.error-message {
    color: var(--error-color);
    font-size: 14px;
    margin-top: 5px;
    display: block;
}

.form-error-message {
    color: var(--error-color);
    font-size: 14px;
    margin-top: 15px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(231, 76, 60, 0.1);
    border-radius: 6px;
    text-align: center;
    font-weight: 500;
}

.form-success-message {
    color: var(--success-color);
    font-size: 14px;
    margin-top: 15px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(46, 204, 113, 0.1);
    border-radius: 6px;
    text-align: center;
    font-weight: 500;
    animation: fadeIn 0.5s ease;
}

.forgot-password {
    display: block;
    text-align: right;
    font-size: 14px;
    color: var(--dark-gray);
    text-decoration: none;
    margin-top: 10px;
}

.forgot-password:hover {
    color: var(--primary-color);
}

.login-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 30px;
    margin-bottom: 30px;
}

.google-login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    background-color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.google-login-btn img {
    width: 20px;
    height: 20px;
}

.google-login-btn:hover {
    background-color: var(--light-gray);
}

.auth-btn {
    width: 100%;
    padding: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.auth-btn:hover {
    background-color: #c0392b;
}

.signup-prompt, .login-prompt {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
    color: var(--dark-gray);
}

.signup-prompt a, .login-prompt a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.signup-prompt a:hover, .login-prompt a:hover {
    text-decoration: underline;
}

.social-login {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-gray);
    text-decoration: none;
    transition: var(--transition);
}

.social-icon:hover {
    background-color: var(--medium-gray);
    color: var(--text-color);
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    transform: translateY(100px);
    opacity: 0;
    z-index: 1000;
}

.toast-notification.success {
    border-left: 4px solid var(--success-color);
}

.toast-notification.success .toast-icon {
    color: var(--success-color);
}

.toast-notification.error {
    border-left: 4px solid var(--error-color);
}

.toast-notification.error .toast-icon {
    color: var(--error-color);
}

.toast-content {
    display: flex;
    align-items: center;
}

.toast-icon {
    font-size: 24px;
    margin-right: 15px;
}

.toast-message {
    font-size: 16px;
    color: var(--text-color);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--white);
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 450px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    cursor: pointer;
    color: var(--dark-gray);
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--error-color);
}

.modal-content h2 {
    margin-bottom: 10px;
    color: var(--secondary-color);
}

.modal-content p {
    margin-bottom: 20px;
    color: var(--dark-gray);
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .login-container {
        flex-direction: column;
        height: auto;
        max-width: 600px;
    }

    .login-image-side {
        min-height: 300px;
    }

    .login-form-side {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .modern-login {
        padding: 10px;
    }

    .login-container {
        border-radius: 15px;
    }

    .login-image-side {
        min-height: 250px;
        padding: 20px;
    }

    .profile-pic {
        width: 40px;
        height: 40px;
    }

    .profile-text h3 {
        font-size: 16px;
    }

    .profile-text p {
        font-size: 12px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 576px) {
    .login-container {
        border-radius: 10px;
    }

    .login-image-side {
        min-height: 200px;
        padding: 15px;
    }

    .login-form-side {
        padding: 20px;
    }

    .form-header {
        margin-bottom: 30px;
    }

    .brand-name {
        font-size: 20px;
    }

    .form-content h2 {
        font-size: 24px;
    }

    .form-subtitle {
        font-size: 14px;
        margin-bottom: 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group input {
        padding: 12px;
    }

    .auth-btn {
        padding: 12px;
    }

    .google-login-btn {
        padding: 12px;
    }

    .social-icon {
        width: 35px;
        height: 35px;
    }
}
