import express from 'express';
import { loginUser,registerUser,adminLogin } from '../controllers/userController.js';
import { register, login, googleLogin, getMe } from '../controllers/authController.js';
import authUser from '../middleware/auth.js';

const userRouter = express.Router();

// Authentication routes
userRouter.post('/register', register);
userRouter.post('/login', login);
userRouter.post('/google-login', googleLogin);
userRouter.get('/me', authUser, getMe);

// Legacy routes (keep for compatibility)
userRouter.post('/legacy-register', registerUser);
userRouter.post('/legacy-login', loginUser);
userRouter.post('/admin', adminLogin);

export default userRouter;