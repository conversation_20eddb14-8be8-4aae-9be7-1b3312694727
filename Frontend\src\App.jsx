import React, { useState, useEffect } from 'react';
import GoogleLoginButton from './components/GoogleLoginButton';

/**
 * Main App Component with Google OAuth Integration
 */

function App() {
    const [user, setUser] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [notification, setNotification] = useState(null);

    useEffect(() => {
        // Check if user is already logged in
        checkAuthState();
    }, []);

    /**
     * Check authentication state on app load
     */
    const checkAuthState = () => {
        try {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');

            if (token && userData) {
                const parsedUser = JSON.parse(userData);
                setUser(parsedUser);
                console.log('✅ User already authenticated:', parsedUser.name);
            }
        } catch (error) {
            console.error('❌ Invalid stored user data');
            localStorage.removeItem('authToken');
            localStorage.removeItem('userData');
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Handle successful Google login
     */
    const handleLoginSuccess = (result) => {
        setUser(result.user);
        showNotification(`Welcome, ${result.user.name}! 🎉`, 'success');
    };

    /**
     * Handle Google login error
     */
    const handleLoginError = (error) => {
        console.error('Login error:', error);
        showNotification(`Login failed: ${error.message}`, 'error');
    };

    /**
     * Handle logout
     */
    const handleLogout = () => {
        // Clear storage
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        
        // Clear state
        setUser(null);
        
        // Disable Google auto-select
        if (window.google && window.google.accounts) {
            window.google.accounts.id.disableAutoSelect();
        }
        
        showNotification('Logged out successfully! 👋', 'success');
    };

    /**
     * Show notification
     */
    const showNotification = (message, type) => {
        setNotification({ message, type });
        setTimeout(() => setNotification(null), 4000);
    };

    /**
     * Loading screen
     */
    if (isLoading) {
        return (
            <div style={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center', 
                height: '100vh',
                fontFamily: 'Arial, sans-serif'
            }}>
                <div>Loading...</div>
            </div>
        );
    }

    return (
        <div style={{ 
            fontFamily: 'Arial, sans-serif',
            maxWidth: '800px',
            margin: '0 auto',
            padding: '20px'
        }}>
            {/* Header */}
            <header style={{ 
                textAlign: 'center', 
                marginBottom: '40px',
                padding: '20px',
                backgroundColor: '#f8f9fa',
                borderRadius: '10px'
            }}>
                <h1 style={{ color: '#333', marginBottom: '10px' }}>
                    StepStyle - Google OAuth Demo
                </h1>
                <p style={{ color: '#666' }}>
                    React + Node.js Google Authentication
                </p>
            </header>

            {/* Notification */}
            {notification && (
                <div style={{
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    padding: '15px 20px',
                    borderRadius: '8px',
                    color: 'white',
                    backgroundColor: notification.type === 'success' ? '#27ae60' : '#e74c3c',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                    zIndex: 1000,
                    animation: 'slideIn 0.3s ease'
                }}>
                    {notification.message}
                </div>
            )}

            {/* Main Content */}
            <main style={{ textAlign: 'center' }}>
                {user ? (
                    /* Authenticated User View */
                    <div style={{
                        backgroundColor: '#fff',
                        padding: '30px',
                        borderRadius: '10px',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                    }}>
                        <h2 style={{ color: '#333', marginBottom: '20px' }}>
                            Welcome to StepStyle! 👋
                        </h2>
                        
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '20px',
                            marginBottom: '30px',
                            flexWrap: 'wrap'
                        }}>
                            {user.profilePicture && (
                                <img 
                                    src={user.profilePicture} 
                                    alt={user.name}
                                    style={{
                                        width: '80px',
                                        height: '80px',
                                        borderRadius: '50%',
                                        border: '3px solid #e74c3c'
                                    }}
                                />
                            )}
                            <div style={{ textAlign: 'left' }}>
                                <h3 style={{ margin: '0 0 5px 0', color: '#333' }}>
                                    {user.name}
                                </h3>
                                <p style={{ margin: '0 0 5px 0', color: '#666' }}>
                                    {user.email}
                                </p>
                                <span style={{
                                    backgroundColor: '#e74c3c',
                                    color: 'white',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    fontSize: '12px'
                                }}>
                                    {user.authProvider || 'google'}
                                </span>
                            </div>
                        </div>

                        <div style={{ marginBottom: '20px' }}>
                            <h4 style={{ color: '#333', marginBottom: '10px' }}>
                                User Data:
                            </h4>
                            <pre style={{
                                backgroundColor: '#f4f4f4',
                                padding: '15px',
                                borderRadius: '8px',
                                textAlign: 'left',
                                overflow: 'auto',
                                fontSize: '12px'
                            }}>
                                {JSON.stringify(user, null, 2)}
                            </pre>
                        </div>

                        <button
                            onClick={handleLogout}
                            style={{
                                padding: '12px 24px',
                                backgroundColor: '#e74c3c',
                                color: 'white',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                fontSize: '14px',
                                fontWeight: '500',
                                transition: 'background-color 0.2s'
                            }}
                            onMouseEnter={(e) => e.target.style.backgroundColor = '#c0392b'}
                            onMouseLeave={(e) => e.target.style.backgroundColor = '#e74c3c'}
                        >
                            Logout
                        </button>
                    </div>
                ) : (
                    /* Login View */
                    <div style={{
                        backgroundColor: '#fff',
                        padding: '40px',
                        borderRadius: '10px',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                    }}>
                        <h2 style={{ color: '#333', marginBottom: '20px' }}>
                            Sign in to StepStyle
                        </h2>
                        <p style={{ color: '#666', marginBottom: '30px' }}>
                            Use your Google account to get started
                        </p>
                        
                        <GoogleLoginButton
                            onSuccess={handleLoginSuccess}
                            onError={handleLoginError}
                            buttonText="Sign in with Google"
                        />
                        
                        <div style={{ marginTop: '30px', fontSize: '12px', color: '#999' }}>
                            <p>🔒 Secure authentication powered by Google OAuth 2.0</p>
                            <p>✅ Your data is protected and never stored without permission</p>
                        </div>
                    </div>
                )}
            </main>

            {/* Footer */}
            <footer style={{
                textAlign: 'center',
                marginTop: '40px',
                padding: '20px',
                color: '#666',
                fontSize: '14px'
            }}>
                <p>© 2024 StepStyle - Google OAuth Integration Demo</p>
                <p>Built with React + Node.js + Express + MongoDB</p>
            </footer>

            {/* CSS for animations */}
            <style jsx>{`
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `}</style>
        </div>
    );
}

export default App;
