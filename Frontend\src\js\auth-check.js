/**
 * Authentication check functionality has been removed
 * This file now provides dummy functions that don't perform any authentication checks
 */

/**
 * Dummy function that always returns true (user is always considered authenticated)
 * @returns {boolean} - Always returns true
 */
export function isAuthenticated() {
    return true;
}

/**
 * Dummy function that doesn't check authentication or redirect
 * @param {boolean} requireAuth - Not used
 * @returns {boolean} - Always returns true
 */
export function checkAuth(requireAuth = true) {
    console.log('Authentication checks have been disabled');
    return true;
}

/**
 * Dummy function that doesn't redirect
 * @returns {boolean} - Always returns false
 */
export function redirectIfAuthenticated() {
    console.log('Authentication redirects have been disabled');
    return false;
}

/**
 * Dummy function that returns null
 * @returns {null} - Always returns null
 */
export function getAuthToken() {
    return null;
}

/**
 * Dummy function that doesn't logout or redirect
 */
export function logout() {
    console.log('Logout functionality has been disabled');
    return;
}

// No initialization needed since authentication has been removed
