* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
    scroll-behavior: smooth;
}

:root {
    --primary-color: #ffffff;
    --secondary-color: #000;
    --accent-color: #ff6464;
    --lightgreen-color:#25ff259f;
    --green-color:#25ff25e0;
    --text-color: #333;
    --light-gray: #f8f8f8;
    --medium-gray: #e0e0e0;
    --dark-gray: #666;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Common Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

section {
    padding: 60px 0;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 20px;
    font-weight: 600;
    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
}

h2 {
    font-size: 2.2rem;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
}

button {
    cursor: pointer;
    border: none;
    outline: none;
    transition: var(--transition);
}

main {
    position: relative;
    width: 100%;

    header {
        height: 100vh;
        width: 100%;
        overflow: hidden;

        #cursor {
            height: 2.8%;
            width: 1.5%;
            background-color: #0a0a0a;
            border-radius: 50%;
            position: fixed;
            z-index: 9;
            box-shadow: 0px 0px 10px 1px #000000;
            display: none;
        }

        nav {
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background-color: transparent;
            backdrop-filter: blur(10px);
            opacity: 1;
            position: fixed;
            top: 0;
            left: 0;
            transition: top 0.3s ease-in-out;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
            z-index: 20;

            #logo {
                height: 7%;
                width: 4%;
                margin-left: 1rem;
                margin-right: 10rem;

                img {
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                }
            }

            #nav-middle {
                display: flex;
                align-items: center;
                gap: 2.5rem;
                justify-content: center;

                a {
                    text-decoration: none;
                    font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                    color: var(--secondary-color);
                    font-weight: 500;
                    position: relative;
                    padding: 5px 0;
                }

                a::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 0;
                    height: 0;
                    transition: var(--transition);
                }

                a:hover::after {
                    width: 0;
                }

                a:hover {
                    color: #11111188;
                }
            }

            #nav-last {
                display: flex;
                gap: 1.4rem;
                align-items: center;
                justify-content: center;
                margin-right: 1rem;

                #search-bar {
                    display: flex;
                    border-radius: 50px;
                    border: 2px solid #111;
                    width: 100%;

                    i {
                        font-size: 1.3em;
                    }

                    .search-icon {
                        margin-left: .2rem;
                        margin-top: .3rem;
                        border-radius: 50% 0 0 50%;
                        background-color: transparent;
                    }

                    #nav-search {
                        border: none;
                        padding: .2rem;
                        width: 10vw;
                        border-radius: 0 50px 50px 0;
                        font-size: 1rem;
                        font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                        outline: none;
                        background-color: transparent;
                        color: black;
                    }

                    #nav-search:active {
                        background-color: transparent;
                    }

                    #nav-search::-webkit-search-cancel-button {
                        cursor: pointer;
                    }
                }

                .cart-icon-container {
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .cart {
                        font-size: 1.5rem;
                        padding: 5px;
                        border-radius: 50%;
                        transition: var(--transition);
                    }

                    .cart-count {
                        position: absolute;
                        top: -8px;
                        right: -8px;
                        background-color: var(--accent-color);
                        color: white;
                        font-size: 0.7rem;
                        font-weight: bold;
                        width: 18px;
                        height: 18px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: transform 0.3s ease;
                    }

                    &.animate .cart-count {
                        animation: cartBounce 0.5s ease;
                    }
                }

                .cart-icon-container:hover,
                .cart-icon-container:active {
                    cursor: pointer;

                    .cart {
                        color: #11111188;
                        transform: scale(1.1);
                    }
                }

                .user {
                    font-size: 1.7rem;
                    transition: var(--transition);
                }

                .user:hover {
                    cursor: pointer;
                    color: #11111188;
                    transform: scale(1.1);
                }
            }
        }

        /* Hero Section */
        #hero {
            height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #fff0f5 0%, #ffd1dc 100%);
            padding-top: 80px;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                width: 100%;
                height: 100%;
                background: url('/assets/images/pattern-dots.png');
                opacity: 0.05;
                z-index: 0;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: -50px;
                right: -50px;
                width: 300px;
                height: 300px;
                background: radial-gradient(circle, rgba(255, 77, 77, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
                border-radius: 50%;
                z-index: 0;
            }

            .hero-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 20px;
                height: 100%;
                position: relative;
                z-index: 1;

                .hero-text {
                    flex: 1;
                    padding-right: 40px;

                    h1 {
                        font-size: 4rem;
                        margin-bottom: 20px;
                        line-height: 1.1;
                        color: var(--secondary-color);
                        font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                        font-weight: 700;
                        letter-spacing: -0.5px;
                        position: relative;

                        &::after {
                            content: '';
                            position: absolute;
                            bottom: -10px;
                            left: 0;
                            width: 100px;
                            height: 4px;
                            background-color: var(--accent-color);
                        }
                    }

                    p {
                        font-size: 1.3rem;
                        color: var(--dark-gray);
                        margin-bottom: 40px;
                        line-height: 1.6;
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                        max-width: 90%;
                    }

                    .buy-now-btn {
                        background-color: var(--secondary-color);
                        color: white;
                        padding: 15px 35px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border-radius: 30px;
                        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                        position: relative;
                        overflow: hidden;
                        z-index: 1;
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
                        letter-spacing: 0.5px;
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                    }

                    .buy-now-btn:active {
                        transform:  scale(1);
                    }
                }

                .hero-image {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: relative;

                    &::before {
                        content: '';
                        position: absolute;
                        width: 400px;
                        height: 400px;
                        background: radial-gradient(circle, rgba(255, 77, 77, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
                        border-radius: 50%;
                        z-index: -1;
                        animation: pulse 6s infinite alternate ease-in-out;
                    }

                    &::after {
                        content: '';
                        position: absolute;
                        width: 300px;
                        height: 300px;
                        background: radial-gradient(circle, rgba(0, 0, 0, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
                        border-radius: 50%;
                        z-index: -1;
                        animation: pulse 8s infinite alternate-reverse ease-in-out;
                    }

                    img {
                        max-width: 100%;
                        height: auto;
                        transform: rotate(-15deg);
                        transition: var(--transition);
                        filter: drop-shadow(0 20px 30px rgba(0, 0, 0, 0.25));
                        animation: float 6s infinite ease-in-out;
                    }

                    img:hover {
                        transform: rotate(-10deg) scale(1.05);
                    }
                }
            }
        }

        @keyframes float {
            0% {
                transform: rotate(-15deg) translateY(0px);
            }
            50% {
                transform: rotate(-12deg) translateY(-15px);
            }
            100% {
                transform: rotate(-15deg) translateY(0px);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.5;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 0.5;
            }
        }
    }

    /* Women's Collection Section */
    #womens-collection {
        padding: 80px 0;
        background-color: white;

        h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                bottom: -15px;
                left: 50%;
                transform: translateX(-50%);
                width: 80px;
                height: 4px;
                background-color: var(--accent-color);
            }
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;

            @media (max-width: 992px) {
                grid-template-columns: repeat(2, 1fr);
            }

            @media (max-width: 576px) {
                grid-template-columns: 1fr;
            }

            .product-card {
                background-color: #f9f9f9;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                display: flex;
                flex-direction: column;

                &:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);

                    .product-image img {
                        transform: scale(1.05);
                    }
                }

                .product-image {
                    width: 100%;
                    height: 400px;
                    overflow: hidden;
                    background-color: #f9f9f9;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    // padding: 15px;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: cover;
                        transition: transform 0.5s ease;
                    }
                }

                .product-info {
                    padding: 15px;
                    display: flex;
                    flex-direction: column;
                    flex-grow: 1;

                    .product-name {
                        font-size: 1rem;
                        font-weight: 600;
                        margin-bottom: 8px;
                        color: #000;
                        font-family: 'Helvetica Now Display', Arial, sans-serif;
                    }

                    .product-price {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 12px;

                        .original-price {
                            font-size: 0.9rem;
                            color: #777;
                            text-decoration: line-through;
                        }

                        .current-price {
                            font-size: 1.1rem;
                            font-weight: 700;
                            color: #000;
                        }

                        .discount-badge {
                            background-color: #ff5a5f;
                            color: white;
                            padding: 2px 6px;
                            border-radius: 4px;
                            font-size: 0.7rem;
                            font-weight: 600;
                        }
                    }

                    .add-to-cart-btn {
                        background-color: #000;
                        color: white;
                        padding: 8px 0;
                        font-size: 0.85rem;
                        font-weight: 600;
                        border-radius: 5px;
                        border: none;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        width: 100%;
                        margin-top: auto;
                        font-family: 'Helvetica Now Display', Arial, sans-serif;

                        &:hover {
                            background-color: #333;
                        }

                        &:active {
                            transform: scale(0.98);
                        }
                    }
                }
            }
        }
    }

    /* Features Section */
    #features {
        background-color: var(--light-gray);
        padding: 80px 0;

        .features-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            gap: 30px;

            .feature {
                flex: 1;
                min-width: 250px;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: var(--shadow);
                text-align: center;
                transition: var(--transition);

                &:hover {
                    transform: translateY(-10px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
                }

                i {
                    font-size: 2.5rem;
                    color: var(--accent-color);
                    margin-bottom: 20px;
                }

                h3 {
                    font-size: 1.3rem;
                    margin-bottom: 15px;
                    color: var(--secondary-color);
                }

                p {
                    color: var(--dark-gray);
                    line-height: 1.6;
                }
            }
        }
    }

    /* Gallery and Price Section */
    #gallery-price {
        padding: 80px 0;
        background-color: white;

        .gallery-price-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .gallery-container {
            display: flex;
            flex-wrap: wrap;
            gap: 40px;

            .gallery-left {
                flex: 1;
                min-width: 300px;
                display: flex;
                gap: 20px;

                .thumbnails {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;

                    .thumbnail {
                        width: 80px;
                        height: 80px;
                        border-radius: 8px;
                        object-fit: cover;
                        cursor: pointer;
                        opacity: 0.7;
                        transition: var(--transition);
                        border: 2px solid transparent;

                        &:hover {
                            opacity: 1;
                        }

                        &.active {
                            opacity: 1;
                            border-color: var(--accent-color);
                        }
                    }
                }

                .main-image {
                    flex: 1;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: var(--shadow);
                    background-color: #F5F5F5;
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        transition: opacity 0.3s ease;
                    }
                }
            }

            .price-container {
                flex: 1;
                min-width: 300px;
                display: flex;
                flex-direction: column;
                gap: 30px;

                .price-info {
                    background-color: var(--light-gray);
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: var(--shadow);

                    h2 {
                        text-align: left;
                        margin-bottom: 20px;

                        &::after {
                            left: 0;
                            transform: none;
                        }
                    }

                    .product-title {
                        font-size: 2rem;
                        font-weight: 700;
                        color: var(--secondary-color);
                        margin-bottom: 15px;
                        font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
                    }

                    .price-display {
                        display: flex;
                        align-items: center;
                        gap: 15px;
                        margin-bottom: 15px;
                        transition: var(--transition);

                        .original-price {
                            font-size: 1.2rem;
                            color: var(--dark-gray);
                            text-decoration: line-through;
                        }

                        .current-price {
                            font-size: 2rem;
                            font-weight: 700;
                            color: var(--secondary-color);
                        }

                        .discount-badge {
                            background-color: var(--accent-color);
                            color: white;
                            padding: 5px 10px;
                            border-radius: 4px;
                            font-size: 0.9rem;
                            font-weight: 600;
                        }
                    }

                    .limited-offer {
                        color: var(--accent-color);
                        font-weight: 600;
                        margin-bottom: 30px;
                    }

                    .product-description {
                        margin-bottom: 30px;

                        h3 {
                            font-size: 1.2rem;
                            margin-bottom: 10px;
                        }

                        p {
                            color: var(--dark-gray);
                            line-height: 1.6;
                        }
                    }

                    .add-to-cart-btn {
                        background-color: var(--secondary-color);
                        color: white;
                        padding: 15px 30px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        border-radius: 30px;
                        width: 100%;
                        transition: var(--transition);
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;

                        &:hover {
                            background-color: var(--accent-color);
                            transform: translateY(-2px);
                        }

                        &:active {
                            transform: translateY(0);
                        }
                    }

                    .back-to-collection-btn {
                        background-color: transparent;
                        color: var(--secondary-color);
                        padding: 15px 30px;
                        font-size: 1rem;
                        font-weight: 600;
                        border: 2px solid var(--secondary-color);
                        border-radius: 30px;
                        width: 100%;
                        margin-top: 15px;
                        transition: var(--transition);
                        font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;

                        &:hover {
                            background-color: var(--secondary-color);
                            color: white;
                        }
                    }
                }

                .size-selector {
                    background-color: var(--light-gray);
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: var(--shadow);

                    h3 {
                        margin-bottom: 20px;
                    }

                    .size-options {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 10px;

                        .size-btn {
                            padding: 10px;
                            background-color: white;
                            border: 1px solid var(--medium-gray);
                            border-radius: 5px;
                            font-size: 0.9rem;
                            transition: var(--transition);

                            &:hover {
                                border-color: var(--secondary-color);
                            }

                            &.active {
                                background-color: var(--secondary-color);
                                color: white;
                                border-color: var(--secondary-color);
                            }
                        }
                    }
                }
            }
        }
    }

    /* Reviews Section */
    #reviews {
        background-color: var(--light-gray);
        padding: 80px 0;

        .reviews-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;

            .review-stats {
                display: flex;
                justify-content: center;
                margin-bottom: 40px;

                .average-rating {
                    text-align: center;

                    .rating-number {
                        font-size: 3rem;
                        font-weight: 700;
                        color: var(--secondary-color);
                    }

                    .stars {
                        margin: 10px 0;
                        color: #FFD700;
                        font-size: 1.5rem;
                    }

                    .total-reviews {
                        color: var(--dark-gray);
                    }
                }
            }

            .review-carousel {
                position: relative;
                max-width: 800px;
                margin: 0 auto;
                overflow: hidden;
                border-radius: 10px;
                box-shadow: var(--shadow);

                .review-slide {
                    display: none;

                    &.active {
                        display: block;
                    }
                }

                .review-content {
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;

                    .reviewer-info {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 10px;

                        .reviewer-name {
                            font-weight: 600;
                            color: var(--secondary-color);
                        }

                        .review-date {
                            color: var(--dark-gray);
                            font-size: 0.9rem;
                        }
                    }

                    .stars {
                        color: #FFD700;
                        margin-bottom: 15px;
                    }

                    .review-text {
                        color: var(--dark-gray);
                        line-height: 1.6;
                        font-style: italic;
                    }
                }
            }

            .carousel-controls {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 20px;
                gap: 20px;

                .prev-btn,
                .next-btn {
                    background-color: white;
                    color: var(--secondary-color);
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 1.5rem;
                    box-shadow: var(--shadow);
                    transition: var(--transition);

                    &:hover {
                        background-color: var(--secondary-color);
                        color: white;
                    }
                }

                .carousel-dots {
                    display: flex;
                    gap: 10px;

                    .dot {
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        background-color: var(--medium-gray);
                        cursor: pointer;
                        transition: var(--transition);

                        &.active {
                            background-color: var(--secondary-color);
                            transform: scale(1.2);
                        }
                    }
                }
            }
        }
    }
}

/* Footer */
footer {
    background-color: var(--secondary-color);
    color: white;
    padding: 60px 0 20px;

    .footer-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        gap: 40px;

        .footer-column {
            flex: 1;
            min-width: 200px;

            h3 {
                font-size: 1.3rem;
                margin-bottom: 20px;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -10px;
                    left: 0;
                    width: 40px;
                    height: 2px;
                    background-color: var(--accent-color);
                }
            }

            ul {
                list-style: none;

                li {
                    margin-bottom: 10px;

                    a {
                        color: #ccc;
                        text-decoration: none;
                        transition: var(--transition);

                        &:hover {
                            color: white;
                        }
                    }
                }
            }

            &.social {
                .social-icons {
                    display: flex;
                    gap: 15px;
                    margin-bottom: 30px;

                    a {
                        color: white;
                        font-size: 1.5rem;
                        transition: var(--transition);

                        &:hover {
                            color: var(--accent-color);
                            transform: translateY(-3px);
                        }
                    }
                }

                .newsletter {
                    h4 {
                        margin-bottom: 15px;
                    }

                    .newsletter-form {
                        display: flex;

                        input {
                            flex: 1;
                            padding: 10px;
                            border: none;
                            border-radius: 4px 0 0 4px;
                            outline: none;
                        }

                        button {
                            background-color: var(--accent-color);
                            color: white;
                            padding: 10px 15px;
                            border: none;
                            border-radius: 0 4px 4px 0;
                            cursor: pointer;
                            transition: var(--transition);

                            &:hover {
                                background-color: #ff4f4f;
                            }
                        }
                    }
                }
            }
        }
    }

    .footer-bottom {
        max-width: 1200px;
        margin: 40px auto 0;
        padding: 20px 20px 0;
        border-top: 1px solid #444;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        gap: 20px;

        .copyright {
            color: #999;
            font-size: 0.9rem;
        }

        .footer-links {
            display: flex;
            gap: 20px;

            a {
                color: #999;
                text-decoration: none;
                font-size: 0.9rem;
                transition: var(--transition);

                &:hover {
                    color: white;
                }
            }
        }
    }
}

/* Cart Modal */
.cart-modal {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background-color: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow-y: auto;
    
    &.open {
        right: 0;
    }

    .cart-modal-content {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .cart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--medium-gray);

        h3 {
            font-size: 1.5rem;
            margin: 0;
            &::before{
                content: '🛒';
                // position: absolute;
                font-size: 1.2rem;
            }
        }

        .close-cart-btn {
            background: none;
            border: none;
            font-size: 1.8rem;
            cursor: pointer;
            color: var(--dark-gray);
            transition: var(--transition);
            border-radius: 50%;
            padding:2px 11px;

            &:hover {
                background-color: #0000000c;
                color: var(--accent-color);
                rotate: 90deg;
            }
        }
    }

    .cart-items {
        flex: 1;
        overflow-y: auto;
        .empty-cart-message {
            text-align: center;
            padding: 50px 0;
            color: var(--dark-gray);
            font-size: 1.1rem;
            font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            p {
                font-size: 1.2rem;
                margin-bottom: 1rem;
            }

            .empty-cart-divider {
                width: 50px;
                height: 3px;
                background-color: var(--medium-gray);
                border-radius: 3px;
            }

            &::before {
                content: '🛒';
                display: block;
                font-size: 3rem;
                margin-bottom: 15px;
                opacity: 0.5;
            }
        }
    }

    .cart-item {
        display: flex;
        gap: 15px;
        padding: 15px;
        border-bottom: 1px solid var(--medium-gray);
        background-color: #fff;
        margin-bottom: 10px;
        border-radius: 8px;
        transition: var(--transition);
        &:hover{
            background-color: #f9f9f9ad;
        }
        .cart-item-image {
            width: 70px;
            height: 70px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #F5F5F5;
            padding: 5px;
            border: 1px solid #e0e0e0;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .cart-item-details {
            flex: 1;

            .cart-item-name {
                font-weight: 600;
                margin-bottom: 5px;
                font-size: 0.95rem;
                color: #333;
            }

            .cart-item-price {
                color: var(--secondary-color);
                font-weight: 600;
                margin-bottom: 10px;
                font-size: 1rem;
            }

            .cart-item-controls {
                display: flex;
                align-items: center;
                gap: 10px;

                .quantity-control {
                    display: flex;
                    align-items: center;
                    border: 1px solid #e0e0e0;
                    border-radius: 20px;
                    overflow: hidden;
                    width: fit-content;
                    background-color: #F8F8F8;
                    padding: 2px 2px;
                    align-items: center;
                    justify-content: center;
                    button {
                        width: 25px;
                        height: 25px;
                        background-color: white;
                        border: .5px solid #7e7c7ca1;
                        font-weight: bold;
                        border-radius: 50%;
                        cursor: pointer;
                        transition: var(--transition);
                        font-size: 1.2rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #333;

                        &:hover {
                            background-color: #111;
                            color: white;
                        }
                    }

                    span {
                        font-weight: 600;
                        padding: 0 10px;
                        min-width: 30px;
                        text-align: center;
                    }
                }

                .remove-item {
                    color: #ff3b30;
                    background: none;
                    border: none;
                    cursor: pointer;
                    font-size: 0.85rem;
                    transition: var(--transition);
                    padding: 5px 10px;
                    border-radius: 4px;
                    margin-left: auto;

                    &:hover {
                        background-color: rgba(255, 59, 48, 0.1);
                    }
                }
            }
        }
    }

    .cart-footer {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid var(--medium-gray);

        .cart-total {
            display: flex;
            justify-content: space-between;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .checkout-btn {
            background-color: var(--secondary-color);
            color: white;
            padding: 15px;
            width: 100%;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-family: 'Funnel Display Medium', 'Helvetica Now Display', sans-serif;

            &:hover {
                background-color: var(--accent-color);
            }
        }
    }
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #4CAF50;
    border-radius: 50px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    z-index: 1000;
    letter-spacing: .5px;
    &.show {
        transform: translateY(0);
        opacity: 1;
    }

    &.success {
        border-left: 4px solid #4CAF50
    }

    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;

        .toast-icon {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .toast-message {
            font-weight: 600;
            color: var(--primary-color);
        }
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    main header #hero .hero-content .hero-text h1 {
        font-size: 3.5rem;
    }
}

@media (max-width: 992px) {
    main header nav #logo {
        margin-right: 2rem;
    }

    main header nav #nav-middle {
        gap: 1.5rem;
    }

    main header #hero .hero-content .hero-text h1 {
        font-size: 3rem;
    }

    main header #hero .hero-content .hero-text p {
        font-size: 1.1rem;
    }

    main #gallery-price .gallery-container {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    main header nav {
        padding: 0.5rem;
    }

    main header nav #logo {
        margin-right: 1rem;
    }

    main header nav #nav-middle {
        display: none;
    }

    main header #hero .hero-content {
        flex-direction: column;
        text-align: center;
        padding-top: 50px;
    }

    main header #hero .hero-content .hero-text {
        padding-right: 0;
        margin-bottom: 30px;
    }

    main header #hero .hero-content .hero-text h1::after {
        left: 50%;
        transform: translateX(-50%);
    }

    main header #hero .hero-content .hero-text p {
        margin: 0 auto 30px;
    }

    main #features .features-container .feature {
        min-width: 100%;
    }

    main #gallery-price .gallery-container .gallery-left {
        flex-direction: column;
    }

    main #gallery-price .gallery-container .gallery-left .thumbnails {
        flex-direction: row;
        overflow-x: auto;
    }

    main #gallery-price .gallery-container .gallery-left .thumbnails .thumbnail {
        width: 60px;
        height: 60px;
    }

    .cart-modal {
        width: 100%;
        right: -100%;
    }
}

@media (max-width: 576px) {
    main header #hero .hero-content .hero-text h1 {
        font-size: 2.5rem;
    }

    main header #hero .hero-content .hero-text p {
        font-size: 1rem;
    }

    main #womens-collection .products-grid {
        grid-template-columns: 1fr;
    }

    footer .footer-container {
        flex-direction: column;
        gap: 30px;
    }

    footer .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}