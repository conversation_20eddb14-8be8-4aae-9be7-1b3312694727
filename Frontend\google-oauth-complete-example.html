<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Google OAuth Example - StepStyle</title>
    <!-- Google Sign-In API -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .step {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .step h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .google-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 16px 24px;
            border: 2px solid #4285f4;
            border-radius: 12px;
            background: white;
            color: #4285f4;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin: 20px 0;
        }
        
        .google-btn:hover {
            background: #4285f4;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 133, 244, 0.3);
        }
        
        .google-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            background: #e8f5e8;
            border: 2px solid #4caf50;
            display: none;
        }
        
        .result.error {
            background: #ffeaea;
            border-color: #f44336;
            color: #d32f2f;
        }
        
        .result.success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
        
        .user-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
            float: left;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .logout-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 15px;
        }
        
        .logout-btn:hover {
            background: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Complete Google OAuth Implementation</h1>
        
        <div class="step">
            <h3>Step 1: Google Login Button</h3>
            <p>Click the button below to start Google OAuth process:</p>
            <button id="google-login-btn" class="google-btn">
                <svg width="20" height="20" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Sign in with Google
            </button>
        </div>
        
        <div class="step">
            <h3>Step 2: Process Flow</h3>
            <p>The complete authentication flow:</p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li>User clicks Google login button</li>
                <li>Google OAuth popup opens</li>
                <li>User authenticates with Google</li>
                <li>Google returns ID token</li>
                <li>Token sent to backend for verification</li>
                <li>Backend creates/updates user and returns JWT</li>
                <li>Frontend stores JWT and user data</li>
            </ol>
        </div>
        
        <div id="result" class="result">
            <div id="result-content"></div>
        </div>
    </div>

    <script>
        // Configuration
        const CONFIG = {
            CLIENT_ID: '142140028318-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com',
            API_KEY: 'AIzaSyAVhYVCyjVM0C1NJhzuMlmi-K7IoZB73yw',
            BACKEND_URL: 'http://localhost:5000/api/user'
        };

        // Google OAuth Implementation
        class GoogleOAuthDemo {
            constructor() {
                this.isInitialized = false;
                this.init();
            }

            async init() {
                try {
                    await this.waitForGoogleAPI();
                    
                    // Initialize Google Sign-In
                    google.accounts.id.initialize({
                        client_id: CONFIG.CLIENT_ID,
                        callback: this.handleCredentialResponse.bind(this)
                    });

                    this.isInitialized = true;
                    this.setupEventListeners();
                    this.checkExistingAuth();
                    
                    console.log('✅ Google OAuth initialized successfully');
                } catch (error) {
                    console.error('❌ Failed to initialize Google OAuth:', error);
                    this.showResult('Failed to initialize Google OAuth: ' + error.message, 'error');
                }
            }

            waitForGoogleAPI() {
                return new Promise((resolve, reject) => {
                    const checkGoogle = () => {
                        if (typeof google !== 'undefined' && google.accounts) {
                            resolve();
                        } else {
                            setTimeout(checkGoogle, 100);
                        }
                    };
                    checkGoogle();
                    
                    setTimeout(() => reject(new Error('Google API failed to load')), 10000);
                });
            }

            setupEventListeners() {
                const loginBtn = document.getElementById('google-login-btn');
                loginBtn.addEventListener('click', () => this.signIn());
            }

            signIn() {
                if (!this.isInitialized) {
                    this.showResult('Google OAuth not initialized', 'error');
                    return;
                }

                this.showLoading(true);
                google.accounts.id.prompt((notification) => {
                    if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                        this.showLoading(false);
                        this.showResult('Google Sign-In was cancelled or not displayed', 'error');
                    }
                });
            }

            async handleCredentialResponse(response) {
                try {
                    this.showResult('🔄 Processing Google authentication...', 'info');
                    
                    // Step 3: Send token to backend
                    const result = await this.verifyTokenWithBackend(response.credential);
                    
                    if (result.success) {
                        // Step 6: Store JWT and user data
                        localStorage.setItem('authToken', result.token);
                        localStorage.setItem('userData', JSON.stringify(result.user));
                        
                        // Step 7: Show success with user info
                        this.showSuccessResult(result);
                    } else {
                        throw new Error(result.message || 'Authentication failed');
                    }
                    
                } catch (error) {
                    console.error('Google login error:', error);
                    this.showResult('❌ Login failed: ' + error.message, 'error');
                } finally {
                    this.showLoading(false);
                }
            }

            // Step 4 & 5: Verify token with backend and get user info
            async verifyTokenWithBackend(token) {
                const response = await fetch(`${CONFIG.BACKEND_URL}/google-login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || 'Backend verification failed');
                }
                
                return await response.json();
            }

            showSuccessResult(result) {
                const { user, token } = result;
                const content = `
                    <h3>✅ Authentication Successful!</h3>
                    <div class="user-info">
                        ${user.profilePicture ? `<img src="${user.profilePicture}" alt="Profile" class="user-avatar">` : ''}
                        <div>
                            <strong>Name:</strong> ${user.name}<br>
                            <strong>Email:</strong> ${user.email}<br>
                            <strong>Auth Provider:</strong> ${user.authProvider}<br>
                            <strong>User ID:</strong> ${user.id}
                        </div>
                        <div style="clear: both;"></div>
                        <button class="logout-btn" onclick="googleOAuthDemo.logout()">Logout</button>
                    </div>
                    <div class="code-block">
                        <strong>JWT Token (first 50 chars):</strong><br>
                        ${token.substring(0, 50)}...
                    </div>
                `;
                this.showResult(content, 'success');
            }

            showResult(message, type) {
                const resultDiv = document.getElementById('result');
                const contentDiv = document.getElementById('result-content');
                
                contentDiv.innerHTML = message;
                resultDiv.className = `result ${type}`;
                resultDiv.style.display = 'block';
            }

            showLoading(show) {
                const loginBtn = document.getElementById('google-login-btn');
                
                if (show) {
                    loginBtn.disabled = true;
                    loginBtn.innerHTML = '<div class="loading"></div> Signing in...';
                } else {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = `
                        <svg width="20" height="20" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Sign in with Google
                    `;
                }
            }

            checkExistingAuth() {
                const token = localStorage.getItem('authToken');
                const userData = localStorage.getItem('userData');
                
                if (token && userData) {
                    const user = JSON.parse(userData);
                    this.showResult(`
                        <h3>🔐 Already Authenticated!</h3>
                        <div class="user-info">
                            ${user.profilePicture ? `<img src="${user.profilePicture}" alt="Profile" class="user-avatar">` : ''}
                            <div>
                                <strong>Name:</strong> ${user.name}<br>
                                <strong>Email:</strong> ${user.email}<br>
                                <strong>Status:</strong> Logged in
                            </div>
                            <div style="clear: both;"></div>
                            <button class="logout-btn" onclick="googleOAuthDemo.logout()">Logout</button>
                        </div>
                    `, 'success');
                }
            }

            logout() {
                localStorage.removeItem('authToken');
                localStorage.removeItem('userData');
                google.accounts.id.disableAutoSelect();
                
                this.showResult('✅ Logged out successfully!', 'success');
                
                setTimeout(() => {
                    document.getElementById('result').style.display = 'none';
                }, 2000);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.googleOAuthDemo = new GoogleOAuthDemo();
        });
    </script>
</body>
</html>
