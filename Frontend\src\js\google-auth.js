// Google OAuth Configuration and Helper Functions
class GoogleAuth {
    constructor() {
        this.clientId = '************-ticjjaltrrq7h998g26ruft3s55rbssl.apps.googleusercontent.com';
        this.isInitialized = false;
        this.init();
    }

    // Initialize Google Sign-In
    async init() {
        try {
            // Wait for Google library to load
            await this.waitForGoogleLibrary();
            
            google.accounts.id.initialize({
                client_id: this.clientId,
                callback: this.handleSignInResponse.bind(this),
                auto_select: false,
                cancel_on_tap_outside: true,
                use_fedcm_for_prompt: true
            });

            this.isInitialized = true;
            console.log('Google Auth initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Google Auth:', error);
        }
    }

    // Wait for Google library to load
    waitForGoogleLibrary() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 50; // 5 seconds max wait
            
            const checkGoogle = () => {
                if (typeof google !== 'undefined' && google.accounts) {
                    resolve();
                } else if (attempts < maxAttempts) {
                    attempts++;
                    setTimeout(checkGoogle, 100);
                } else {
                    reject(new Error('Google library failed to load'));
                }
            };
            
            checkGoogle();
        });
    }

    // Handle sign-in response
    handleSignInResponse(response) {
        try {
            const userInfo = this.parseJWT(response.credential);
            
            if (userInfo) {
                this.saveUserData(userInfo);
                this.showSuccessMessage(userInfo.name);
                this.redirectToHome();
            } else {
                throw new Error('Failed to parse user information');
            }
            
        } catch (error) {
            console.error('Sign-in error:', error);
            this.showErrorMessage('Sign-in failed. Please try again.');
        }
    }

    // Parse JWT token
    parseJWT(token) {
        try {
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(
                atob(base64)
                    .split('')
                    .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
                    .join('')
            );
            return JSON.parse(jsonPayload);
        } catch (error) {
            console.error('JWT parsing error:', error);
            return null;
        }
    }

    // Save user data to localStorage
    saveUserData(userInfo) {
        const userData = {
            id: userInfo.sub,
            name: userInfo.name,
            email: userInfo.email,
            picture: userInfo.picture,
            loginMethod: 'google',
            loginTime: new Date().toISOString()
        };

        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('loginMethod', 'google');
        
        console.log('User data saved:', userData);
    }

    // Show success message
    showSuccessMessage(name) {
        if (typeof showToast === 'function') {
            showToast(`Welcome ${name}! Redirecting...`, 'success');
        } else {
            alert(`Welcome ${name}! Redirecting...`);
        }
    }

    // Show error message
    showErrorMessage(message) {
        if (typeof showToast === 'function') {
            showToast(message, 'error');
        } else {
            alert(message);
        }
    }

    // Redirect to home page
    redirectToHome() {
        setTimeout(() => {
            // Check if we're in a subdirectory
            const currentPath = window.location.pathname;
            if (currentPath.includes('/Pages/')) {
                window.location.href = '../index.html';
            } else {
                window.location.href = 'index.html';
            }
        }, 1500);
    }

    // Trigger sign-in popup
    signIn() {
        if (!this.isInitialized) {
            this.showErrorMessage('Google Sign-In not ready. Please refresh the page.');
            return;
        }

        google.accounts.id.prompt((notification) => {
            if (notification.isNotDisplayed()) {
                console.log('Google Sign-In prompt not displayed');
                this.showErrorMessage('Please enable popups and try again.');
            } else if (notification.isSkippedMoment()) {
                console.log('Google Sign-In prompt skipped');
                this.showErrorMessage('Sign-in was cancelled.');
            }
        });
    }

    // Sign out
    signOut() {
        // Clear local storage
        localStorage.removeItem('user');
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('loginMethod');
        
        // Revoke Google token if available
        if (this.isInitialized && google.accounts.id) {
            google.accounts.id.disableAutoSelect();
        }
        
        console.log('User signed out');
        
        // Redirect to login page
        window.location.href = 'Pages/login.html';
    }

    // Check if user is logged in
    isLoggedIn() {
        return localStorage.getItem('isLoggedIn') === 'true';
    }

    // Get current user data
    getCurrentUser() {
        const userData = localStorage.getItem('user');
        return userData ? JSON.parse(userData) : null;
    }
}

// Create global instance
window.googleAuth = new GoogleAuth();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GoogleAuth;
}
