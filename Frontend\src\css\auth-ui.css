/* Authentication UI Styles */

/* User Dropdown */
.user-dropdown {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.user-dropdown .user-icon,
.user-dropdown .ph-user-circle {
    font-size: 1.8rem;
    color: #111;
    transition: color 0.3s ease;
}

.user-dropdown .user-icon:hover,
.user-dropdown .ph-user-circle:hover {
    color: #11111188;
}

/* Dropdown Content */
.dropdown-content {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: #fff;
    min-width: 120px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-content.show-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-content a {
    color: #333;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    font-family: 'Helvetica Now Display', Helvetica, Arial, sans-serif;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.dropdown-content a:hover {
    background-color: #f5f5f5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dropdown-content {
        right: -10px;
    }
}
