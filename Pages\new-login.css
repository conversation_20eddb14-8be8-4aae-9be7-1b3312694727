/* Modern Login Page Styles - Based on Design */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #e74c3c;
    --secondary-color: #2c3e50;
    --text-color: #333;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #6c757d;
    --white: #ffffff;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--light-gray);
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

.login-container {
    display: flex;
    height: 100vh;
    width: 100%;
}

/* Left side with image */
.login-image-side {
    flex: 1;
    background: linear-gradient(135deg, #4a4a4a 0%, #2c2c2c 100%);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px;
    color: white;
}

.top-nav {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.brand-name {
    font-size: 24px;
    font-weight: 700;
    color: white;
}

.shoe-image {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.shoe-image img {
    max-width: 80%;
    max-height: 400px;
    object-fit: contain;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

.profile-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.profile-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    background: white;
    padding: 8px;
}

.profile-text h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.profile-text p {
    font-size: 14px;
    opacity: 0.8;
}

.profile-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Right side with forms */
.login-form-side {
    flex: 1;
    background: white;
    padding: 40px 60px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.brand-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    letter-spacing: 2px;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--dark-gray);
    cursor: pointer;
    font-size: 14px;
}

.form-content {
    display: none;
    flex: 1;
}

.form-content.active {
    display: block;
}

.form-content h2 {
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.form-subtitle {
    color: var(--dark-gray);
    margin-bottom: 40px;
    font-size: 16px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 16px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    font-size: 16px;
    background-color: var(--light-gray);
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: white;
}

.password-input-container {
    position: relative;
}

.toggle-password {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--dark-gray);
    font-size: 18px;
}

.forgot-password {
    display: block;
    text-align: right;
    margin-top: 8px;
    color: var(--dark-gray);
    text-decoration: none;
    font-size: 14px;
}

.forgot-password:hover {
    color: var(--primary-color);
}

.login-options {
    margin: 32px 0;
}

.google-login-btn {
    width: 100%;
    padding: 16px;
    border: 1px solid var(--medium-gray);
    border-radius: 8px;
    background: white;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;
    transition: var(--transition);
}

.google-login-btn:hover {
    background-color: var(--light-gray);
}

.google-login-btn i {
    font-size: 20px;
    color: #4285f4;
}

.auth-btn {
    width: 100%;
    padding: 16px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.auth-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.signup-prompt,
.login-prompt {
    text-align: center;
    margin-top: 24px;
    color: var(--dark-gray);
    font-size: 14px;
}

.tab-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.tab-link:hover {
    text-decoration: underline;
}

.form-options {
    margin: 24px 0;
}

.terms-conditions {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--dark-gray);
}

.terms-conditions input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.terms-conditions a {
    color: var(--primary-color);
    text-decoration: none;
}

.terms-conditions a:hover {
    text-decoration: underline;
}

.error-message {
    color: var(--primary-color);
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(400px);
    transition: var(--transition);
    z-index: 1000;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-icon {
    font-size: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
    }
    
    .login-image-side {
        flex: 0 0 200px;
        padding: 20px;
    }
    
    .shoe-image {
        padding: 20px 0;
    }
    
    .shoe-image img {
        max-height: 120px;
    }
    
    .profile-section {
        padding: 15px;
    }
    
    .login-form-side {
        padding: 30px 20px;
    }
    
    .form-content h2 {
        font-size: 24px;
    }
}
