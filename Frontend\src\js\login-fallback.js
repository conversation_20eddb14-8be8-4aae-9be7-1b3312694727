/**
 * Login & Registration functionality for StepStyle website (Fallback version)
 * This is a non-module version that doesn't rely on ES modules
 */

// API URL - Update with your actual backend URL
const API_URL = 'http://localhost:4000/api';

// DOM Elements
let tabButtons;
let formContents;
let loginForm;
let registerForm;
let forgotPasswordForm;
let forgotPasswordLink;
let forgotPasswordModal;
let resetPasswordModal;
let resetPasswordForm;
let closeModalBtns;
let togglePasswordButtons;
let googleLoginBtn;
let toast;

// Debug flag - set to true to see debug messages in console
const DEBUG = true;

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    console.log('Login fallback script loaded');

    // Initialize DOM elements
    tabButtons = document.querySelectorAll('.tab-btn');
    formContents = document.querySelectorAll('.form-content');
    loginForm = document.getElementById('login');
    registerForm = document.getElementById('register');
    forgotPasswordForm = document.getElementById('forgot-password-form');
    forgotPasswordLink = document.getElementById('forgot-password-link');
    forgotPasswordModal = document.getElementById('forgot-password-modal');
    resetPasswordModal = document.getElementById('reset-password-modal');
    resetPasswordForm = document.getElementById('reset-password-form');
    closeModalBtns = document.querySelectorAll('.close-modal');
    togglePasswordButtons = document.querySelectorAll('.toggle-password');
    googleLoginBtn = document.querySelector('.google-login-btn');
    toast = document.querySelector('.toast-notification');

    // Set up tab switching
    setupTabs();

    // Set up password visibility toggle
    setupPasswordToggle();

    // Set up form submissions
    setupFormSubmissions();

    // Check URL parameters for tab selection
    checkTabFromURL();

    // Show a welcome toast
    showToast('Welcome to StepStyle!', 'success');
});

/**
 * Check URL parameters for tab selection
 */
function checkTabFromURL() {
    console.log('Checking URL parameters for tab selection...');
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    console.log('Tab parameter:', tab);

    if (tab === 'register') {
        console.log('Register tab requested from URL');

        // Remove active class from all form contents
        formContents.forEach(content => content.classList.remove('active'));

        // Activate the register form
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.classList.add('active');
            console.log('Activated register form from URL parameter');
        } else {
            console.error('Register form not found');
        }
    }
}

/**
 * Clear all form error messages
 */
function clearFormErrors() {
    // Remove any existing form error messages
    const errorElements = document.querySelectorAll('.form-error-message');
    errorElements.forEach(element => {
        element.remove();
    });

    // Clear field error messages
    const fieldErrorElements = document.querySelectorAll('.error-message');
    fieldErrorElements.forEach(element => {
        element.textContent = '';
    });
}

/**
 * Set up tab switching functionality
 */
function setupTabs() {
    console.log('Setting up tabs...');

    // Tab links inside the forms (Sign up / Sign in links)
    const tabLinks = document.querySelectorAll('.tab-link');
    console.log('Tab links:', tabLinks);

    tabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Tab link clicked:', link.getAttribute('data-tab'));

            // Clear any error messages
            clearFormErrors();

            // Remove active class from all form contents
            formContents.forEach(content => content.classList.remove('active'));

            // Add active class to corresponding form content
            const tabId = link.getAttribute('data-tab');
            const formElement = document.getElementById(`${tabId}-form`);

            if (formElement) {
                formElement.classList.add('active');
                console.log(`Activated form: ${tabId}-form`);
            } else {
                console.error(`Form element not found: ${tabId}-form`);
            }
        });
    });
}

/**
 * Set up password visibility toggle
 */
function setupPasswordToggle() {
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Find the password input within the same container
            const container = button.closest('.password-input-container');
            const passwordInput = container ? container.querySelector('input') : button.previousElementSibling;

            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                button.classList.remove('ri-eye-line');
                button.classList.add('ri-eye-off-line');
            } else {
                passwordInput.type = 'password';
                button.classList.remove('ri-eye-off-line');
                button.classList.add('ri-eye-line');
            }
        });
    });
}

/**
 * Set up form submissions
 */
function setupFormSubmissions() {
    // Login form submission
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Get form values
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            if (DEBUG) {
                console.log('Login form submitted');
                console.log('Email:', email);
                console.log('Password:', password.replace(/./g, '*'));
            }

            // Basic validation
            if (!email || !password) {
                showToast('Please fill in all fields', 'error');
                return;
            }

            try {
                // Show processing message
                showToast('Logging in...', 'success');

                if (DEBUG) {
                    console.log('Sending login request to:', `${API_URL}/user/login`);
                }

                // Send login request to API
                const response = await fetch(`${API_URL}/user/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (DEBUG) {
                    console.log('Login response status:', response.status);
                }

                const data = await response.json();

                if (DEBUG) {
                    console.log('Login response data:', data);
                }

                if (data.success) {
                    // Save token to localStorage
                    localStorage.setItem('auth-token', data.token);

                    // Show success message
                    showToast('Login successful! Redirecting...', 'success');

                    // Create a success message element
                    const successMessage = document.createElement('div');
                    successMessage.className = 'form-success-message';
                    successMessage.textContent = 'Login successful! You will be redirected to the home page.';

                    // Insert after the form
                    loginForm.appendChild(successMessage);

                    // Redirect to home page after 1.5 seconds
                    setTimeout(() => {
                        window.location.href = '../index.html';
                    }, 1500);
                } else {
                    // Show error message
                    let errorMessage = 'Login failed. Please try again.';

                    if (data.message === "User doesn't exists" || data.message === "Invalid credentials") {
                        errorMessage = 'User or email and password is wrong';
                    } else if (data.message) {
                        errorMessage = data.message;
                    }

                    showToast(errorMessage, 'error');

                    // Create an error message element
                    const errorElement = document.createElement('div');
                    errorElement.className = 'form-error-message';
                    errorElement.textContent = errorMessage;

                    // Insert after the form
                    const loginOptions = loginForm.querySelector('.login-options');
                    if (loginOptions) {
                        loginOptions.parentNode.insertBefore(errorElement, loginOptions.nextSibling);
                    } else {
                        loginForm.appendChild(errorElement);
                    }
                }
            } catch (error) {
                console.error('Login error:', error);
                showToast('An error occurred. Please try again later.', 'error');

                // Create an error message element
                const errorElement = document.createElement('div');
                errorElement.className = 'form-error-message';
                errorElement.textContent = 'Connection error. Please check if the backend server is running.';

                // Insert after the form
                const loginOptions = loginForm.querySelector('.login-options');
                if (loginOptions) {
                    loginOptions.parentNode.insertBefore(errorElement, loginOptions.nextSibling);
                } else {
                    loginForm.appendChild(errorElement);
                }
            }
        });
    }

    // Register form submission
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            // Get form values
            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;

            if (DEBUG) {
                console.log('Register form submitted');
                console.log('Name:', name);
                console.log('Email:', email);
                console.log('Password:', password.replace(/./g, '*'));
            }

            // Basic validation
            if (!name || !email || !password) {
                showToast('Please fill in all fields', 'error');
                return;
            }

            if (password.length < 8) {
                showToast('Password must be at least 8 characters', 'error');
                return;
            }

            try {
                // Show processing message
                showToast('Creating your account...', 'success');

                if (DEBUG) {
                    console.log('Sending register request to:', `${API_URL}/user/register`);
                }

                // Send register request to API
                const response = await fetch(`${API_URL}/user/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, email, password })
                });

                if (DEBUG) {
                    console.log('Register response status:', response.status);
                }

                const data = await response.json();

                if (DEBUG) {
                    console.log('Register response data:', data);
                }

                if (data.success) {
                    // Save token to localStorage
                    localStorage.setItem('auth-token', data.token);

                    // Show success message
                    showToast('Registration successful! Redirecting to home page...', 'success');

                    // Create a success message element
                    const successMessage = document.createElement('div');
                    successMessage.className = 'form-success-message';
                    successMessage.textContent = 'Account created successfully! You will be redirected to the home page.';

                    // Insert before the login prompt
                    const loginPrompt = registerForm.querySelector('.login-prompt');
                    if (loginPrompt) {
                        loginPrompt.parentNode.insertBefore(successMessage, loginPrompt);
                    } else {
                        registerForm.appendChild(successMessage);
                    }

                    // Redirect to home page after 1.5 seconds
                    setTimeout(() => {
                        window.location.href = '../index.html';
                    }, 1500);
                } else {
                    // Show error message
                    let errorMessage = data.message || 'Registration failed. Please try again.';
                    showToast(errorMessage, 'error');

                    // Create an error message element
                    const errorElement = document.createElement('div');
                    errorElement.className = 'form-error-message';
                    errorElement.textContent = errorMessage;

                    // Insert before the register button
                    const registerButton = registerForm.querySelector('.auth-btn');
                    if (registerButton) {
                        registerButton.parentNode.insertBefore(errorElement, registerButton);
                    } else {
                        registerForm.appendChild(errorElement);
                    }
                }
            } catch (error) {
                console.error('Registration error:', error);
                showToast('An error occurred. Please try again later.', 'error');

                // Create an error message element
                const errorElement = document.createElement('div');
                errorElement.className = 'form-error-message';
                errorElement.textContent = 'Connection error. Please check if the backend server is running.';

                // Insert before the register button
                const registerButton = registerForm.querySelector('.auth-btn');
                if (registerButton) {
                    registerButton.parentNode.insertBefore(errorElement, registerButton);
                } else {
                    registerForm.appendChild(errorElement);
                }
            }
        });
    }
}

/**
 * Show toast notification
 * @param {string} message - Message to display
 * @param {string} type - Toast type (success or error)
 */
function showToast(message, type = 'success') {
    console.log('Showing toast:', message, type);

    // Check if toast element exists
    if (!toast) {
        console.error('Toast element not found');
        return;
    }

    // Set toast message and type
    const toastMessage = document.querySelector('.toast-message');
    if (toastMessage) {
        toastMessage.textContent = message;
    } else {
        console.error('Toast message element not found');
    }

    // Remove existing classes and add new type class
    toast.classList.remove('success', 'error');
    toast.classList.add(type);

    // Fallback animation with CSS
    toast.style.transform = 'translateY(0)';
    toast.style.opacity = '1';

    // Hide toast after 3 seconds
    setTimeout(() => {
        toast.style.transform = 'translateY(100px)';
        toast.style.opacity = '0';
    }, 3000);
}
