<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Google Auth</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .google-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 12px 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            color: #333;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 0;
        }
        .google-btn:hover {
            background: #f8f9fa;
            border-color: #ccc;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .user-info img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
            vertical-align: middle;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google OAuth Test Page</h1>
        <p>This page tests the Google OAuth integration for StepStyle.</p>
        
        <div id="status" class="status info">
            Initializing Google Sign-In...
        </div>
        
        <button class="google-btn" id="google-signin-btn">
            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" width="20" height="20" />
            Sign in with Google
        </button>
        
        <button class="google-btn" id="signout-btn" style="display: none;">
            Sign Out
        </button>
        
        <div id="user-info" style="display: none;"></div>
        
        <h3>Debug Information:</h3>
        <pre id="debug-info">Waiting for initialization...</pre>
        
        <h3>Configuration:</h3>
        <pre>Client ID: 314126416131-ticjjaltrrq7h998g26ruft3s55rbssl.apps.googleusercontent.com
Project ID: login-user-461318</pre>
    </div>

    <script>
        const CLIENT_ID = '314126416131-ticjjaltrrq7h998g26ruft3s55rbssl.apps.googleusercontent.com';
        let isInitialized = false;
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function updateDebug(info) {
            document.getElementById('debug-info').textContent = JSON.stringify(info, null, 2);
        }
        
        function showUserInfo(userInfo) {
            const userInfoEl = document.getElementById('user-info');
            userInfoEl.innerHTML = `
                <div class="user-info">
                    <img src="${userInfo.picture}" alt="Profile Picture">
                    <strong>${userInfo.name}</strong><br>
                    Email: ${userInfo.email}<br>
                    ID: ${userInfo.sub}
                </div>
            `;
            userInfoEl.style.display = 'block';
            document.getElementById('signout-btn').style.display = 'block';
            document.getElementById('google-signin-btn').style.display = 'none';
        }
        
        function handleCredentialResponse(response) {
            try {
                updateStatus('Processing sign-in...', 'info');
                
                // Parse JWT token
                const base64Url = response.credential.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                
                const userInfo = JSON.parse(jsonPayload);
                
                updateStatus('Sign-in successful!', 'success');
                updateDebug({
                    status: 'success',
                    userInfo: userInfo,
                    timestamp: new Date().toISOString()
                });
                
                showUserInfo(userInfo);
                
                // Save to localStorage
                localStorage.setItem('user', JSON.stringify(userInfo));
                localStorage.setItem('isLoggedIn', 'true');
                
            } catch (error) {
                updateStatus('Error processing sign-in: ' + error.message, 'error');
                updateDebug({
                    status: 'error',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }
        
        function initializeGoogleSignIn() {
            if (typeof google !== 'undefined') {
                try {
                    google.accounts.id.initialize({
                        client_id: CLIENT_ID,
                        callback: handleCredentialResponse,
                        auto_select: false,
                        cancel_on_tap_outside: true
                    });
                    
                    isInitialized = true;
                    updateStatus('Google Sign-In ready!', 'success');
                    updateDebug({
                        status: 'initialized',
                        clientId: CLIENT_ID,
                        timestamp: new Date().toISOString()
                    });
                    
                } catch (error) {
                    updateStatus('Initialization error: ' + error.message, 'error');
                    updateDebug({
                        status: 'initialization_error',
                        error: error.message,
                        timestamp: new Date().toISOString()
                    });
                }
            } else {
                updateStatus('Google library not loaded, retrying...', 'error');
                setTimeout(initializeGoogleSignIn, 1000);
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', initializeGoogleSignIn);
        
        // Sign-in button click handler
        document.getElementById('google-signin-btn').addEventListener('click', function() {
            if (isInitialized) {
                google.accounts.id.prompt((notification) => {
                    updateDebug({
                        status: 'prompt_result',
                        notification: {
                            isDisplayed: notification.isDisplayed(),
                            isNotDisplayed: notification.isNotDisplayed(),
                            isSkippedMoment: notification.isSkippedMoment()
                        },
                        timestamp: new Date().toISOString()
                    });
                    
                    if (notification.isNotDisplayed()) {
                        updateStatus('Sign-in popup blocked. Please allow popups.', 'error');
                    } else if (notification.isSkippedMoment()) {
                        updateStatus('Sign-in was cancelled.', 'error');
                    }
                });
            } else {
                updateStatus('Google Sign-In not ready. Please refresh the page.', 'error');
            }
        });
        
        // Sign-out button click handler
        document.getElementById('signout-btn').addEventListener('click', function() {
            localStorage.removeItem('user');
            localStorage.removeItem('isLoggedIn');
            
            document.getElementById('user-info').style.display = 'none';
            document.getElementById('signout-btn').style.display = 'none';
            document.getElementById('google-signin-btn').style.display = 'block';
            
            updateStatus('Signed out successfully!', 'success');
            updateDebug({
                status: 'signed_out',
                timestamp: new Date().toISOString()
            });
        });
        
        // Check if user is already logged in
        const savedUser = localStorage.getItem('user');
        if (savedUser) {
            try {
                const userInfo = JSON.parse(savedUser);
                showUserInfo(userInfo);
                updateStatus('Already signed in!', 'success');
            } catch (error) {
                localStorage.removeItem('user');
                localStorage.removeItem('isLoggedIn');
            }
        }
    </script>
</body>
</html>
