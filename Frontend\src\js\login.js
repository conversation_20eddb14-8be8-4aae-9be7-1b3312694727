/**
 * Login & Registration functionality for StepStyle website
 */

// Import GSAP if available
let gsap;
try {
    // Try to use the global gsap object if it exists
    if (window.gsap) {
        gsap = window.gsap;
        console.log("Using global GSAP");
    } else {
        // If not available, create a simple fallback
        console.warn("GSAP not found, using fallback animation");
        gsap = {
            timeline: function() {
                return {
                    to: function(element, props) {
                        // Simple fallback animation
                        if (typeof element === 'string') {
                            element = document.querySelector(element);
                        }
                        if (element && props) {
                            Object.keys(props).forEach(key => {
                                if (key !== 'duration' && key !== 'ease' && key !== 'delay') {
                                    element.style[key] = props[key];
                                }
                            });
                        }
                        return this;
                    }
                };
            }
        };
    }
} catch (error) {
    console.warn("Error initializing GSAP, using fallback:", error);
    // Create a fallback gsap object
    gsap = {
        timeline: function() {
            return {
                to: function(element, props) {
                    // Simple fallback animation
                    if (typeof element === 'string') {
                        element = document.querySelector(element);
                    }
                    if (element && props) {
                        Object.keys(props).forEach(key => {
                            if (key !== 'duration' && key !== 'ease' && key !== 'delay') {
                                element.style[key] = props[key];
                            }
                        });
                    }
                    return this;
                }
            };
        }
    };
}

// Define updateAuthUI function if it's not imported
let updateAuthUI;
try {
    // Try to import from auth-ui.js
    const module = await import("./auth-ui.js");
    updateAuthUI = module.updateAuthUI;
    console.log("Successfully imported updateAuthUI");
} catch (error) {
    console.warn("Failed to import updateAuthUI, using fallback:", error);
    // Create a fallback function
    updateAuthUI = function() {
        console.log("Fallback updateAuthUI called");
    };
}

// API URL - Update with your actual backend URL
const API_URL = 'http://localhost:4000/api';

// DOM Elements
const tabButtons = document.querySelectorAll('.tab-btn');
const formContents = document.querySelectorAll('.form-content');
const loginForm = document.getElementById('login');
const registerForm = document.getElementById('register');
const forgotPasswordForm = document.getElementById('forgot-password-form');
const forgotPasswordLink = document.getElementById('forgot-password-link');
const forgotPasswordModal = document.getElementById('forgot-password-modal');
const resetPasswordModal = document.getElementById('reset-password-modal');
const resetPasswordForm = document.getElementById('reset-password-form');
const closeModalBtns = document.querySelectorAll('.close-modal');
const togglePasswordButtons = document.querySelectorAll('.toggle-password');
const googleLoginBtn = document.querySelector('.google-login-btn');
const toast = document.querySelector('.toast-notification');

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Set up tab switching
    setupTabs();

    // Set up password visibility toggle
    setupPasswordToggle();

    // Set up form submissions
    setupFormSubmissions();

    // Set up forgot password modal
    setupForgotPassword();

    // Set up Google login button
    setupGoogleLogin();

    // Check URL parameters for tab selection
    checkTabFromURL();

    // Check if user is already logged in
    checkAuthStatus();
});

/**
 * Check URL parameters for tab selection
 */
function checkTabFromURL() {
    console.log('Checking URL parameters for tab selection...');
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    console.log('Tab parameter:', tab);

    if (tab === 'register') {
        console.log('Register tab requested from URL');

        // Remove active class from all form contents
        formContents.forEach(content => content.classList.remove('active'));

        // Activate the register form
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.classList.add('active');
            console.log('Activated register form from URL parameter');
        } else {
            console.error('Register form not found');
        }
    }
}

/**
 * Clear all form error messages
 */
function clearFormErrors() {
    // Remove any existing form error messages
    const errorElements = document.querySelectorAll('.form-error-message');
    errorElements.forEach(element => {
        element.remove();
    });

    // Clear field error messages
    const fieldErrorElements = document.querySelectorAll('.error-message');
    fieldErrorElements.forEach(element => {
        element.textContent = '';
    });
}

/**
 * Set up tab switching functionality
 */
function setupTabs() {
    console.log('Setting up tabs...');

    // Tab links inside the forms (Sign up / Sign in links)
    const tabLinks = document.querySelectorAll('.tab-link');
    console.log('Tab links:', tabLinks);

    tabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Tab link clicked:', link.getAttribute('data-tab'));

            // Clear any error messages
            clearFormErrors();

            // Remove active class from all form contents
            formContents.forEach(content => content.classList.remove('active'));

            // Add active class to corresponding form content
            const tabId = link.getAttribute('data-tab');
            const formElement = document.getElementById(`${tabId}-form`);

            if (formElement) {
                formElement.classList.add('active');
                console.log(`Activated form: ${tabId}-form`);
            } else {
                console.error(`Form element not found: ${tabId}-form`);
            }
        });
    });
}

/**
 * Set up password visibility toggle
 */
function setupPasswordToggle() {
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Find the password input within the same container
            const container = button.closest('.password-input-container');
            const passwordInput = container ? container.querySelector('input') : button.previousElementSibling;

            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                button.classList.remove('ri-eye-line');
                button.classList.add('ri-eye-off-line');
            } else {
                passwordInput.type = 'password';
                button.classList.remove('ri-eye-off-line');
                button.classList.add('ri-eye-line');
            }
        });
    });
}

/**
 * Set up form submissions
 */
function setupFormSubmissions() {
    // Login form submission
    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Clear any previous form errors
        clearFormErrors();

        // Get form values
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;

        // Validate form
        if (!validateLoginForm(email, password)) {
            return;
        }

        try {
            // Send login request to API
            const response = await fetch(`${API_URL}/user/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });

            const data = await response.json();

            if (data.success) {
                console.log('Login successful, token:', data.token);

                try {
                    // Save token to localStorage
                    localStorage.setItem('auth-token', data.token);
                    console.log('Token saved to localStorage');

                    // Try to update the auth UI if the function exists
                    if (typeof updateAuthUI === 'function') {
                        updateAuthUI();
                        console.log('Auth UI updated');
                    }

                    // Show success message
                    showToast('Login successful! Redirecting...', 'success');

                    // Check if there's a return URL in the query parameters
                    const urlParams = new URLSearchParams(window.location.search);
                    const returnUrl = urlParams.get('returnUrl');

                    console.log('Return URL:', returnUrl);

                    // Redirect to return URL or home page after 1.5 seconds
                    setTimeout(() => {
                        if (returnUrl) {
                            window.location.href = decodeURIComponent(returnUrl);
                        } else {
                            // Use absolute path to ensure correct redirection
                            const basePath = window.location.pathname.substring(0, window.location.pathname.indexOf('/Pages'));
                            window.location.href = basePath + '/index.html';
                        }
                    }, 1500);
                } catch (error) {
                    console.error('Error during login success handling:', error);
                    alert('Login successful, but there was an error with the redirect. Please try again.');
                }
            } else {
                // Create or update the login error message element
                let loginErrorElement = document.getElementById('login-form-error');
                if (!loginErrorElement) {
                    loginErrorElement = document.createElement('div');
                    loginErrorElement.id = 'login-form-error';
                    loginErrorElement.className = 'form-error-message';

                    // Insert after the forgot password link
                    const forgotPasswordLink = document.getElementById('forgot-password-link');
                    if (forgotPasswordLink) {
                        forgotPasswordLink.parentNode.insertBefore(loginErrorElement, forgotPasswordLink.nextSibling);
                    } else {
                        // Fallback - insert before the login options
                        const loginOptions = document.querySelector('.login-options');
                        if (loginOptions) {
                            loginOptions.parentNode.insertBefore(loginErrorElement, loginOptions);
                        }
                    }
                }

                // Set the error message
                if (data.message === "User doesn't exists") {
                    loginErrorElement.textContent = 'User or email and password is wrong';
                } else if (data.message === "Invalid credentials") {
                    loginErrorElement.textContent = 'User or email and password is wrong';
                } else {
                    loginErrorElement.textContent = data.message || 'Login failed. Please try again.';
                }
            }
        } catch (error) {
            console.error('Login error:', error);

            // Create or update the login error message element
            let loginErrorElement = document.getElementById('login-form-error');
            if (!loginErrorElement) {
                loginErrorElement = document.createElement('div');
                loginErrorElement.id = 'login-form-error';
                loginErrorElement.className = 'form-error-message';

                // Insert after the forgot password link
                const forgotPasswordLink = document.getElementById('forgot-password-link');
                if (forgotPasswordLink) {
                    forgotPasswordLink.parentNode.insertBefore(loginErrorElement, forgotPasswordLink.nextSibling);
                } else {
                    // Fallback - insert before the login options
                    const loginOptions = document.querySelector('.login-options');
                    if (loginOptions) {
                        loginOptions.parentNode.insertBefore(loginErrorElement, loginOptions);
                    }
                }
            }

            loginErrorElement.textContent = 'An error occurred. Please try again later.';
        }
    });

    // Register form submission
    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Clear any previous form errors
        clearFormErrors();

        // Get form values
        const name = document.getElementById('register-name').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;

        // Validate form
        if (!validateRegisterForm(name, email, password)) {
            return;
        }

        try {
            // Send register request to API
            const response = await fetch(`${API_URL}/user/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, email, password })
            });

            const data = await response.json();

            if (data.success) {
                console.log('Registration successful, token:', data.token);

                try {
                    // Save token to localStorage
                    localStorage.setItem('auth-token', data.token);
                    console.log('Token saved to localStorage');

                    // Try to update the auth UI if the function exists
                    if (typeof updateAuthUI === 'function') {
                        updateAuthUI();
                        console.log('Auth UI updated');
                    }

                    // Show success message
                    showToast('Registration successful! Redirecting to home page...', 'success');

                    // Create a success message element
                    const successMessage = document.createElement('div');
                    successMessage.className = 'form-success-message';
                    successMessage.textContent = 'Account created successfully! You will be redirected to the home page.';

                    // Insert before the login prompt
                    const loginPrompt = registerForm.querySelector('.login-prompt');
                    if (loginPrompt) {
                        loginPrompt.parentNode.insertBefore(successMessage, loginPrompt);
                    }

                    // Check if there's a return URL in the query parameters
                    const urlParams = new URLSearchParams(window.location.search);
                    const returnUrl = urlParams.get('returnUrl');

                    console.log('Return URL:', returnUrl);

                    // Redirect to home page after 1.5 seconds
                    setTimeout(() => {
                        // Use absolute path to ensure correct redirection
                        const basePath = window.location.pathname.substring(0, window.location.pathname.indexOf('/Pages'));
                        window.location.href = basePath + '/index.html';
                    }, 1500);
                } catch (error) {
                    console.error('Error during registration success handling:', error);
                    alert('Registration successful, but there was an error with the redirect. Please try again.');
                }
            } else {
                // Create or update the register error message element
                let registerErrorElement = document.getElementById('register-form-error');
                if (!registerErrorElement) {
                    registerErrorElement = document.createElement('div');
                    registerErrorElement.id = 'register-form-error';
                    registerErrorElement.className = 'form-error-message';

                    // Insert before the register button
                    const registerButton = registerForm.querySelector('.auth-btn');
                    if (registerButton) {
                        registerButton.parentNode.insertBefore(registerErrorElement, registerButton);
                    }
                }

                // Set the error message
                registerErrorElement.textContent = data.message || 'Registration failed. Please try again.';
            }
        } catch (error) {
            console.error('Registration error:', error);

            // Create or update the register error message element
            let registerErrorElement = document.getElementById('register-form-error');
            if (!registerErrorElement) {
                registerErrorElement = document.createElement('div');
                registerErrorElement.id = 'register-form-error';
                registerErrorElement.className = 'form-error-message';

                // Insert before the register button
                const registerButton = registerForm.querySelector('.auth-btn');
                if (registerButton) {
                    registerButton.parentNode.insertBefore(registerErrorElement, registerButton);
                }
            }

            registerErrorElement.textContent = 'An error occurred. Please try again later.';
        }
    });
}

/**
 * Validate login form
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {boolean} - Whether form is valid
 */
function validateLoginForm(email, password) {
    let isValid = true;

    // Reset error messages
    document.getElementById('login-email-error').textContent = '';
    document.getElementById('login-password-error').textContent = '';

    // Validate email
    if (!email) {
        document.getElementById('login-email-error').textContent = 'Email is required';
        isValid = false;
    } else if (!isValidEmail(email)) {
        document.getElementById('login-email-error').textContent = 'Please enter a valid email';
        isValid = false;
    }

    // Validate password
    if (!password) {
        document.getElementById('login-password-error').textContent = 'Password is required';
        isValid = false;
    }

    return isValid;
}

/**
 * Validate register form
 * @param {string} name - User name
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {boolean} - Whether form is valid
 */
function validateRegisterForm(name, email, password) {
    let isValid = true;

    // Reset error messages
    document.getElementById('register-name-error').textContent = '';
    document.getElementById('register-email-error').textContent = '';
    document.getElementById('register-password-error').textContent = '';

    // Validate name
    if (!name) {
        document.getElementById('register-name-error').textContent = 'Name is required';
        isValid = false;
    }

    // Validate email
    if (!email) {
        document.getElementById('register-email-error').textContent = 'Email is required';
        isValid = false;
    } else if (!isValidEmail(email)) {
        document.getElementById('register-email-error').textContent = 'Please enter a valid email';
        isValid = false;
    }

    // Validate password
    if (!password) {
        document.getElementById('register-password-error').textContent = 'Password is required';
        isValid = false;
    } else if (password.length < 8) {
        document.getElementById('register-password-error').textContent = 'Password must be at least 8 characters';
        isValid = false;
    }

    return isValid;
}

/**
 * Check if email is valid
 * @param {string} email - Email to validate
 * @returns {boolean} - Whether email is valid
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Show toast notification
 * @param {string} message - Message to display
 * @param {string} type - Toast type (success or error)
 */
function showToast(message, type = 'success') {
    console.log('Showing toast:', message, type);

    // Check if toast element exists
    if (!toast) {
        console.error('Toast element not found');
        return;
    }

    // Set toast message and type
    const toastMessage = document.querySelector('.toast-message');
    if (toastMessage) {
        toastMessage.textContent = message;
    } else {
        console.error('Toast message element not found');
    }

    // Remove existing classes and add new type class
    toast.classList.remove('success', 'error');
    toast.classList.add(type);

    try {
        // Create animation timeline
        const toastTl = gsap.timeline();

        // Show toast
        toastTl.to(toast, {
            y: 0,
            opacity: 1,
            duration: 0.5,
            ease: "back.out(1.7)"
        });

        // Hide toast after 3 seconds
        toastTl.to(toast, {
            y: 100,
            opacity: 0,
            duration: 0.5,
            ease: "power3.in",
            delay: 3
        });
    } catch (error) {
        console.warn('Error using GSAP for toast animation, using fallback:', error);

        // Fallback animation with CSS
        toast.style.transform = 'translateY(0)';
        toast.style.opacity = '1';

        // Hide toast after 3 seconds
        setTimeout(() => {
            toast.style.transform = 'translateY(100px)';
            toast.style.opacity = '0';
        }, 3000);
    }
}

/**
 * Set up forgot password modal and functionality
 */
function setupForgotPassword() {
    // Check URL parameters for reset token
    const urlParams = new URLSearchParams(window.location.search);
    const resetToken = urlParams.get('reset');
    const resetEmail = urlParams.get('email');

    // If reset token is present, show reset password modal
    if (resetToken && resetEmail) {
        // Check if token is valid
        const resetRequests = JSON.parse(localStorage.getItem('password-reset-requests') || '{}');
        const request = resetRequests[resetEmail];

        if (request && request.token === resetToken && request.expires > Date.now()) {
            // Set token and email in hidden fields
            document.getElementById('reset-token').value = resetToken;
            document.getElementById('reset-email-value').value = resetEmail;

            // Show reset password modal
            resetPasswordModal.classList.add('active');
        } else {
            // Token is invalid or expired
            showToast('Password reset link is invalid or has expired. Please request a new one.', 'error');
        }

        // Remove reset parameters from URL without refreshing the page
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    // Open forgot password modal when forgot password link is clicked
    forgotPasswordLink.addEventListener('click', (e) => {
        e.preventDefault();
        forgotPasswordModal.classList.add('active');
    });

    // Close modals when close buttons are clicked
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            forgotPasswordModal.classList.remove('active');
            resetPasswordModal.classList.remove('active');
        });
    });

    // Close modals when clicking outside the modal content
    window.addEventListener('click', (e) => {
        if (e.target === forgotPasswordModal) {
            forgotPasswordModal.classList.remove('active');
        }
        if (e.target === resetPasswordModal) {
            resetPasswordModal.classList.remove('active');
        }
    });

    // Handle forgot password form submission
    forgotPasswordForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const email = document.getElementById('reset-email').value;
        const errorElement = document.getElementById('reset-email-error');

        // Validate email
        if (!email) {
            errorElement.textContent = 'Email is required';
            return;
        }

        if (!isValidEmail(email)) {
            errorElement.textContent = 'Please enter a valid email';
            return;
        }

        // Clear error message
        errorElement.textContent = '';

        try {
            // Show loading state
            const submitButton = forgotPasswordForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;
            submitButton.textContent = 'Sending...';
            submitButton.disabled = true;

            // First check if the user exists in the database
            const checkUserResponse = await fetch(`${API_URL}/user/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    password: 'checkUserOnly' // This is just to check if the user exists
                })
            });

            const checkUserData = await checkUserResponse.json();

            // If user doesn't exist, show error
            if (checkUserData.message === "User doesn't exists") {
                errorElement.textContent = 'No account found with this email address';
                submitButton.textContent = originalButtonText;
                submitButton.disabled = false;
                return;
            }

            // Generate a reset token (this would normally be done on the backend)
            const resetToken = Math.random().toString(36).substring(2, 15) +
                               Math.random().toString(36).substring(2, 15);

            // Store the reset token and email in localStorage (in a real app, this would be stored in the database)
            const resetRequests = JSON.parse(localStorage.getItem('password-reset-requests') || '{}');
            resetRequests[email] = {
                token: resetToken,
                expires: Date.now() + 3600000 // Token expires in 1 hour
            };
            localStorage.setItem('password-reset-requests', JSON.stringify(resetRequests));

            // Create reset link
            const resetLink = `${window.location.origin}${window.location.pathname}?reset=${resetToken}&email=${encodeURIComponent(email)}`;

            // In a real application, you would send this link via email
            // For demonstration, we'll show it in the console and in an alert
            console.log('Password Reset Link:', resetLink);

            // Simulate sending email
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Close the modal
            forgotPasswordModal.classList.remove('active');

            // Show success message
            showToast('Password reset link sent to your email!', 'success');

            // Reset the form
            forgotPasswordForm.reset();

            // Reset button state
            submitButton.textContent = originalButtonText;
            submitButton.disabled = false;

            // Show alert with reset link (for demonstration only)
            setTimeout(() => {
                alert(`For demonstration purposes, here is your reset link:\n\n${resetLink}\n\nIn a real application, this would be sent to your email.`);
            }, 1000);
        } catch (error) {
            console.error('Forgot password error:', error);
            errorElement.textContent = 'An error occurred. Please try again later.';
        }
    });

    // Handle reset password form submission
    resetPasswordForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        const token = document.getElementById('reset-token').value;
        const email = document.getElementById('reset-email-value').value;

        const newPasswordError = document.getElementById('new-password-error');
        const confirmPasswordError = document.getElementById('confirm-password-error');

        // Clear previous error messages
        newPasswordError.textContent = '';
        confirmPasswordError.textContent = '';

        // Validate password
        if (!newPassword) {
            newPasswordError.textContent = 'Password is required';
            return;
        }

        if (newPassword.length < 8) {
            newPasswordError.textContent = 'Password must be at least 8 characters';
            return;
        }

        // Validate confirm password
        if (!confirmPassword) {
            confirmPasswordError.textContent = 'Please confirm your password';
            return;
        }

        if (newPassword !== confirmPassword) {
            confirmPasswordError.textContent = 'Passwords do not match';
            return;
        }

        try {
            // Show loading state
            const submitButton = resetPasswordForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;
            submitButton.textContent = 'Resetting...';
            submitButton.disabled = true;

            // In a real application, you would send a request to your backend
            // to update the user's password. For now, we'll simulate it.

            // Simulate API call with a timeout
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Remove the reset request from localStorage
            const resetRequests = JSON.parse(localStorage.getItem('password-reset-requests') || '{}');
            delete resetRequests[email];
            localStorage.setItem('password-reset-requests', JSON.stringify(resetRequests));

            // Close the modal
            resetPasswordModal.classList.remove('active');

            // Show success message
            showToast('Password has been reset successfully! You can now log in with your new password.', 'success');

            // Reset the form
            resetPasswordForm.reset();

            // Reset button state
            submitButton.textContent = originalButtonText;
            submitButton.disabled = false;

            // Switch to login tab
            tabButtons.forEach(btn => {
                if (btn.getAttribute('data-tab') === 'login') {
                    btn.click();
                }
            });
        } catch (error) {
            console.error('Reset password error:', error);
            confirmPasswordError.textContent = 'An error occurred. Please try again later.';

            // Reset button state
            const submitButton = resetPasswordForm.querySelector('button[type="submit"]');
            submitButton.textContent = 'Reset Password';
            submitButton.disabled = false;
        }
    });
}

/**
 * Set up Google login button functionality
 */
function setupGoogleLogin() {
    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', () => {
            // In a real application, this would integrate with Google OAuth
            // For demonstration purposes, we'll show a toast notification
            showToast('Google login would be implemented with OAuth in a production environment', 'success');

            // Simulate successful login after 2 seconds
            setTimeout(() => {
                // Create a demo token
                localStorage.setItem('auth-token', 'google-demo-token');

                // Show success message
                showToast('Google login successful! Redirecting...', 'success');

                // Redirect to home page after 1.5 seconds
                setTimeout(() => {
                    window.location.href = '../index.html';
                }, 1500);
            }, 2000);
        });
    }
}

/**
 * Check if user is already authenticated
 */
function checkAuthStatus() {
    console.log('Checking authentication status...');

    try {
        const token = localStorage.getItem('auth-token');
        console.log('Auth token:', token ? 'exists' : 'not found');

        if (token) {
            // User is already logged in
            console.log('User is already logged in');

            // Check if there's a return URL in the query parameters
            const urlParams = new URLSearchParams(window.location.search);
            const returnUrl = urlParams.get('returnUrl');
            console.log('Return URL:', returnUrl);

            if (returnUrl) {
                // Redirect to the return URL
                console.log('Redirecting to return URL:', decodeURIComponent(returnUrl));
                window.location.href = decodeURIComponent(returnUrl);
            } else {
                // Redirect to home page using absolute path
                const basePath = window.location.pathname.substring(0, window.location.pathname.indexOf('/Pages'));
                const homePath = basePath + '/index.html';
                console.log('Redirecting to home page:', homePath);
                window.location.href = homePath;
            }
        } else {
            console.log('User is not logged in, staying on login page');
        }
    } catch (error) {
        console.error('Error checking auth status:', error);
    }
}
