/* Import common styles */
@import url('../styles/style.css');

:root {
  --primary-color: #111;
  --secondary-color: #4361ee;
  --accent-color: #3a56d4;
  --light-bg: #f8f9fa;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --error-color: #f44336;
  --text-color: #333;
  --light-text: #757575;
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Helvetica Now Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--text-color);
  background-color: #fff;
  line-height: 1.5;
}



/* Checkout Container Styles */
.checkout-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.checkout-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.back-link {
  color: var(--text-color);
  text-decoration: none;
  font-size: 1.5rem;
  margin-right: 1rem;
}

.checkout-container h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-color);
}

.checkout-content {
  display: flex;
  gap: 2.5rem;
  position: relative; /* Add position relative for proper positioning */
}

/* Checkout Steps Styles */
.checkout-steps {
  flex: 1.5;
}

.checkout-step {
  margin-bottom: 2rem;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.step-header {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  cursor: pointer;
  position: relative;
}

.step-number {
  font-weight: 600;
  margin-right: 0.5rem;
  color: var(--text-color);
}

.step-header h2 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  flex-grow: 1;
}

.step-arrow {
  font-size: 1.5rem;
  color: var(--light-text);
  transition: transform 0.3s ease;
}

.checkout-step.active .step-arrow {
  transform: rotate(180deg);
}

.step-content {
  padding: 1.5rem 0;
  display: none;
}

.checkout-step.active .step-content {
  display: block;
}

/* Disabled step styling */
.checkout-step.disabled {
  opacity: 0.7;
}

.checkout-step.disabled .step-header {
  cursor: not-allowed;
}

.step-lock {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem;
  color: #888;
  font-size: 1.2rem;
}

.checkout-step:not(.disabled) .step-lock {
  display: none;
}

/* Form Styles */
.form-row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  flex: 1;
}

label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--light-text);
  letter-spacing: 0.5px;
}

input[type="text"],
input[type="tel"],
input[type="email"],
select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  transition: all 0.3s;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
select:focus {
  border-color: var(--secondary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

/* Phone Input Styles */
.phone-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.phone-input-container {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: border-color 0.3s;
}

.phone-group.error .phone-input-container,
.phone-input-container.error {
  border-color: var(--error-color);
}

.country-code {
  display: flex;
  align-items: center;
  background-color: var(--light-bg);
  border-right: 1px solid var(--border-color);
  min-width: 120px;
  position: relative;
}

/* Custom Select Styling */
.custom-select {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.selected-option {
  padding: 0.75rem 0;
  text-align: center;
  font-size: 0.9rem;
}

.arrow-container {
  position: absolute;
  top: 1.9px;
  left: 5;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.arrow-container i {
  font-size: 1.2rem;
  color: var(--light-text);
}

.flag-icon {
  width: 20px;
  height: 15px;
  margin-right: 0.5rem;
}

.phone-input-container input {
  border: none;
  flex: 1;
}

.input-success {
  position: absolute;
  right: 10px;
  top: 38px; /* Position it at the input field level, not at the label */
  color: var(--success-color);
  font-size: 1.2rem;
  z-index: 2;
}

.pin-success {
  top: 38px; /* Position it at the input field level, not at the label */
}

/* Delivery Options Styles */
.delivery-options {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.delivery-option {
  flex: 1;
  position: relative;
  cursor: pointer;
}

.delivery-option input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all 0.3s;
}

.delivery-option.selected .option-content {
  border-color: var(--secondary-color);
  background-color: rgba(67, 97, 238, 0.05);
}

.option-content i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

/* Date and Time Input Styles */
.date-input, .time-input {
  position: relative;
}

.date-input i, .time-input i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--light-text);
}

/* Select Container Styles */
.select-container {
  position: relative;
}

.select-container select {
  appearance: none;
  padding-right: 2rem;
}

.select-container i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--light-text);
  pointer-events: none;
}

/* Payment Options Styles */
.payment-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.payment-option {
  flex: 1;
  min-width: 120px;
  position: relative;
  cursor: pointer;
}

.payment-option input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.payment-option .option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all 0.3s;
  height: 70px;
  overflow: hidden;
}

.payment-option.selected .option-content {
  border-color: var(--secondary-color);
  background-color: rgba(67, 97, 238, 0.05);
}

.payment-option img {
  height: 30px;
  max-width: 100%;
  object-fit: contain;
}

.payment-option span {
  font-weight: 600;
  font-size: 0.9rem;
  margin-top: 5px;
  text-align: center;
}

/* Payment Gateway Styles */
.payment-gateway {
  margin-top: 2rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1.5rem;
}

.payment-form {
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.payment-form h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.payment-form p {
  margin-bottom: 1rem;
  color: var(--text-color);
  font-size: 0.9rem;
}

.secure-payment-info {
  display: flex;
  align-items: center;
  margin-top: 1.5rem;
  padding: 0.75rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: var(--border-radius);
}

.secure-payment-info i {
  color: var(--success-color);
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.secure-payment-info span {
  font-size: 0.8rem;
  color: var(--text-color);
}

.upi-apps {
  margin-top: 1.5rem;
}

.upi-apps p {
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.upi-app-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.upi-app {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  width: 100px;
  text-align: center;
}

.upi-app:hover {
  border-color: var(--secondary-color);
  background-color: rgba(67, 97, 238, 0.05);
}

.upi-app input {
  position: absolute;
  opacity: 0;
}

.upi-app-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upi-app-icon svg {
  width: 100%;
  height: 100%;
}

.upi-app span {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Selected UPI app styling */
.upi-app input:checked + .upi-app-icon + span,
.upi-app input:checked ~ span {
  font-weight: 600;
  color: var(--secondary-color);
}

.upi-app input:checked ~ .upi-app-icon,
.upi-app input:checked + .upi-app-icon {
  transform: scale(1.1);
}

/* UPI ID input styling */
.upi-id-input {
  position: relative;
  display: flex;
  align-items: center;
}

.upi-id-info {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary-color);
  cursor: pointer;
}

.upi-id-info i {
  font-size: 1.2rem;
}

.upi-id-info .tooltip {
  position: absolute;
  top: -40px;
  right: 0;
  width: 250px;
  background-color: #333;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  z-index: 10;
}

.upi-id-info:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* UPI ID confirmation styling */
.upi-id-confirmation {
  display: flex;
  align-items: center;
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--success-color);
}

.upi-id-confirmation i {
  color: var(--success-color);
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.upi-id-confirmation span {
  font-size: 0.9rem;
  color: var(--text-color);
}

.upi-id-confirmation strong {
  font-weight: 600;
  color: var(--secondary-color);
}

/* UPI Payment Action Styles */
.upi-payment-action {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.upi-pay-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1rem;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.upi-pay-button:hover {
  background-color: #3651d8;
}

.upi-pay-button i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.upi-payment-status {
  margin-top: 1rem;
}

.payment-pending {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: var(--border-radius);
  border-left: 3px solid #ff9800;
}

.payment-pending i {
  color: #ff9800;
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.payment-pending span {
  font-size: 0.9rem;
  color: var(--text-color);
}

.payment-pending strong {
  font-weight: 600;
  color: var(--secondary-color);
}

/* Stripe Elements Styles */
.stripe-element {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: white;
  transition: all 0.3s;
  min-height: 40px;
}

.stripe-element.StripeElement--focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

.stripe-element.StripeElement--invalid {
  border-color: var(--error-color);
}

.stripe-errors {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.5rem;
  min-height: 20px;
}

.stripe-payment-action {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.stripe-pay-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1rem;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.stripe-pay-button:hover {
  background-color: #3651d8;
}

.stripe-pay-button i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.stripe-payment-status {
  margin-top: 1rem;
}

.payment-processing {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--secondary-color);
}

.payment-processing i {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.payment-processing span {
  font-size: 0.9rem;
  color: var(--text-color);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stripe-badges {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.stripe-badge {
  height: 24px;
}

.card-badges {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.card-badge {
  height: 30px;
}

/* UPI Payment Action Styles */
.upi-payment-action {
  margin-top: 1.5rem;
}

.upi-pay-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 1rem;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
}

.upi-pay-button:hover {
  background-color: #3651d8;
}

.upi-pay-button i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.upi-payment-status {
  margin-top: 1rem;
}

.payment-pending {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: var(--border-radius);
  border-left: 3px solid #ff9800;
}

.payment-pending i {
  color: #ff9800;
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.payment-pending span {
  font-size: 0.9rem;
  color: var(--text-color);
}

.payment-pending strong {
  font-weight: 600;
  color: var(--secondary-color);
}

.cod-note {
  display: flex;
  align-items: center;
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: var(--border-radius);
}

.cod-note i {
  color: #ff9800;
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.cod-note span {
  font-size: 0.8rem;
  color: var(--text-color);
}

/* Payment Gateway Styles */
.payment-gateway {
  margin-top: 2rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1.5rem;
}

.payment-form {
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.payment-form h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.payment-form p {
  margin-bottom: 1rem;
  color: var(--text-color);
  font-size: 0.9rem;
}

.secure-payment-info {
  display: flex;
  align-items: center;
  margin-top: 1.5rem;
  padding: 0.75rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: var(--border-radius);
}

.secure-payment-info i {
  color: var(--success-color);
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.secure-payment-info span {
  font-size: 0.8rem;
  color: var(--text-color);
}

.upi-apps {
  margin-top: 1.5rem;
}

.upi-apps p {
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.upi-app-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.upi-app {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
}

.upi-app:hover {
  border-color: var(--secondary-color);
}

.upi-app input {
  margin-right: 0.5rem;
}

.cod-note {
  display: flex;
  align-items: center;
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: var(--border-radius);
}

.cod-note i {
  color: #ff9800;
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.cod-note span {
  font-size: 0.8rem;
  color: var(--text-color);
}

/* Order Summary Styles */
.order-summary {
  flex: 1;
  background-color: var(--light-bg);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  align-self: flex-start;
  display: flex;
  flex-direction: column;
  max-height: 600px; /* Set a max height for the entire order summary */
}

.order-summary h2 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.order-items {
  margin-bottom: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
  /* Add scrollbar styling for better visibility */
  scrollbar-width: thin;
  scrollbar-color: var(--secondary-color) var(--light-bg);
}

/* Scrollbar styling for Webkit browsers */
.order-items::-webkit-scrollbar {
  width: 6px;
}

.order-items::-webkit-scrollbar-track {
  background: var(--light-bg);
}

.order-items::-webkit-scrollbar-thumb {
  background-color: var(--secondary-color);
  border-radius: 6px;
}

/* Style for when there are many items */
.order-items.many-items {
  max-height: 250px; /* Slightly reduced height to ensure it's obvious there's more content */
  overflow-y: scroll; /* Force scrollbar to be visible */
  box-shadow: inset 0 -5px 5px -5px rgba(0, 0, 0, 0.1); /* Add shadow at bottom to indicate scrollable content */
}

.order-item {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.order-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.order-item img {
  width: 70px;
  height: 70px;
  border-radius: var(--border-radius);
  object-fit: cover;
  margin-right: 1rem;
}

.order-item-details {
  flex: 1;
}

.order-item-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.order-item-price {
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.order-item-quantity {
  font-size: 0.8rem;
  color: var(--light-text);
}

.empty-cart {
  text-align: center;
  padding: 2rem 0;
  color: var(--light-text);
}

/* Size and color details removed as requested */

.price-details {
  margin-bottom: 1.5rem;
}

.original-price {
  display: flex;
  align-items: center;
}

.price-label {
  text-decoration: line-through;
  color: var(--light-text);
  margin-right: 0.75rem;
}

.discount-price {
  font-size: 1.2rem;
  font-weight: 600;
}

.order-total {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
  margin-bottom: 1.5rem;
}

.subtotal, .discount, .shipping, .total {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.total {
  font-weight: 600;
  font-size: 1rem;
  margin-top: 0.5rem;
}

/* Checkout Actions Styles */
.checkout-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.primary-button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  width: 100%;
}

.primary-button:hover {
  background-color: var(--accent-color);
}

.terms-agreement {
  display: flex;
  align-items: flex-start;
  font-size: 0.8rem;
  color: var(--light-text);
}

.terms-agreement input {
  margin-right: 0.5rem;
  margin-top: 0.25rem;
}

.terms-agreement a {
  color: var(--secondary-color);
  text-decoration: none;
}

/* Form Validation Styles */
input.error {
  border-color: var(--error-color);
}

.error-message {
  color: var(--error-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  display: block;
  clear: both;
}

.phone-group .error-message {
  margin-top: 0.5rem;
  margin-bottom: 0;
}

/* Toast Notification Styles */
.toast-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius);
  padding: 1rem;
  display: flex;
  align-items: center;
  max-width: 300px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s;
  z-index: 1000;
}

.toast-notification.show {
  transform: translateY(0);
  opacity: 1;
}

.toast-icon {
  margin-right: 0.75rem;
  font-size: 1.2rem;
}

.toast-notification.success .toast-icon {
  color: var(--success-color);
}

.toast-notification.error .toast-icon {
  color: var(--error-color);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .checkout-content {
    flex-direction: column;
  }

  .order-summary {
    margin-top: 2rem;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1.5rem;
  }

  .checkout-container {
    padding: 0 1.5rem;
  }
}

@media (max-width: 480px) {
  .payment-options, .delivery-options {
    flex-wrap: wrap;
  }

  .payment-option, .delivery-option {
    flex: 0 0 calc(50% - 0.5rem);
  }

  .checkout-header {
    margin-top: 1rem;
  }
}
