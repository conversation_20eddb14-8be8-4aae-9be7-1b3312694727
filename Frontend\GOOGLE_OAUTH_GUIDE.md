# Google OAuth Implementation Guide - StepStyle

## 🚀 Complete React + Node.js Google Authentication

### 📋 Table of Contents
1. [Overview](#overview)
2. [Backend Setup](#backend-setup)
3. [Frontend Setup](#frontend-setup)
4. [React Components](#react-components)
5. [Usage Examples](#usage-examples)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)

---

## 📖 Overview

This implementation provides complete Google OAuth 2.0 authentication for your React + Node.js application using your credentials:

**Client ID:** `142140028318-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com`

### ✨ Features
- ✅ Secure Google OAuth 2.0 integration
- ✅ JWT token-based authentication
- ✅ React hooks for state management
- ✅ Automatic token verification
- ✅ User profile management
- ✅ Session persistence
- ✅ Error handling & loading states

---

## 🔧 Backend Setup

### 1. Dependencies
```bash
cd Backend
npm install google-auth-library jsonwebtoken
```

### 2. Environment Variables (.env)
```env
GOOGLE_CLIENT_ID="142140028318-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
JWT_SECRET="your_super_secret_jwt_key"
JWT_EXPIRE="7d"
```

### 3. Backend Route
```javascript
// routes/authRoutes.js
router.post('/google-login', googleLogin);
```

### 4. Controller Implementation
The `googleLogin` function in `controllers/authController.js`:
- Verifies Google ID token
- Creates/updates user in database
- Generates JWT token
- Returns user data and token

---

## 🎨 Frontend Setup

### 1. Include Google API Script
```html
<script src="https://accounts.google.com/gsi/client" async defer></script>
```

### 2. React Hook Usage
```jsx
import useAuth from './hooks/useAuth';

function LoginPage() {
    const { loginWithGoogle, user, isLoading, error } = useAuth();
    
    const handleGoogleLogin = async (credential) => {
        const result = await loginWithGoogle(credential);
        if (result.success) {
            console.log('Login successful!', result.user);
        }
    };
    
    return (
        <GoogleLoginButton onSuccess={handleGoogleLogin} />
    );
}
```

### 3. Google Login Button Component
```jsx
import GoogleLoginButton from './components/GoogleLoginButton';

<GoogleLoginButton
    onSuccess={(result) => console.log('Success:', result)}
    onError={(error) => console.log('Error:', error)}
    buttonText="Sign in with Google"
/>
```

---

## 🧩 React Components

### GoogleLoginButton Component
**Location:** `src/components/GoogleLoginButton.jsx`

**Props:**
- `onSuccess`: Callback for successful login
- `onError`: Callback for login errors
- `buttonText`: Custom button text

**Features:**
- Automatic Google API loading
- Loading states
- Error handling
- Responsive design

### useAuth Hook
**Location:** `src/hooks/useAuth.js`

**Returns:**
```javascript
{
    // State
    user,              // Current user object
    isLoading,         // Loading state
    isAuthenticated,   // Authentication status
    error,             // Error message
    
    // Methods
    loginWithGoogle,   // Login function
    logout,            // Logout function
    refreshAuth,       // Refresh auth state
    updateUserProfile, // Update user data
    
    // Utilities
    getToken,          // Get auth token
    isTokenValid,      // Check token validity
    authenticatedFetch // Make authenticated requests
}
```

---

## 💡 Usage Examples

### 1. Basic Login Page
```jsx
import React from 'react';
import useAuth from '../hooks/useAuth';
import GoogleLoginButton from '../components/GoogleLoginButton';

function LoginPage() {
    const { loginWithGoogle, isLoading } = useAuth();
    
    const handleSuccess = async (result) => {
        console.log('Welcome!', result.user.name);
        // Redirect to dashboard
        window.location.href = '/dashboard';
    };
    
    const handleError = (error) => {
        alert(`Login failed: ${error.message}`);
    };
    
    return (
        <div className="login-page">
            <h1>Welcome to StepStyle</h1>
            <GoogleLoginButton
                onSuccess={handleSuccess}
                onError={handleError}
                buttonText="Continue with Google"
            />
        </div>
    );
}
```

### 2. Protected Route
```jsx
import React from 'react';
import useAuth from '../hooks/useAuth';

function ProtectedPage() {
    const { user, isAuthenticated, isLoading, logout } = useAuth();
    
    if (isLoading) return <div>Loading...</div>;
    
    if (!isAuthenticated) {
        return <div>Please login to access this page</div>;
    }
    
    return (
        <div>
            <h1>Welcome, {user.name}!</h1>
            <img src={user.profilePicture} alt={user.name} />
            <p>Email: {user.email}</p>
            <button onClick={logout}>Logout</button>
        </div>
    );
}
```

### 3. Making Authenticated API Calls
```jsx
import useAuth from '../hooks/useAuth';

function UserProfile() {
    const { authenticatedFetch, user } = useAuth();
    
    const updateProfile = async () => {
        try {
            const response = await authenticatedFetch('/api/user/profile', {
                method: 'PUT',
                body: JSON.stringify({ name: 'New Name' })
            });
            
            const data = await response.json();
            console.log('Profile updated:', data);
        } catch (error) {
            console.error('Update failed:', error);
        }
    };
    
    return (
        <div>
            <h2>Profile</h2>
            <button onClick={updateProfile}>Update Profile</button>
        </div>
    );
}
```

---

## 🧪 Testing

### 1. Test File
Open `Frontend/test-google-oauth.html` in your browser to test the implementation.

### 2. Backend Testing
```bash
cd Backend
npm start
# Server should start on http://localhost:5000
```

### 3. Frontend Testing
```bash
cd Frontend
# Open login.html in browser or serve with a local server
```

### 4. Test Steps
1. Click "Test Google Login" button
2. Complete Google authentication
3. Check console for success/error messages
4. Verify user data is stored in localStorage

---

## 🔍 Troubleshooting

### Common Issues

#### 1. "Google API not loaded"
**Solution:** Ensure Google script is loaded before initializing
```html
<script src="https://accounts.google.com/gsi/client" async defer></script>
```

#### 2. "Invalid client ID"
**Solution:** Verify your client ID in:
- `.env` file
- Frontend JavaScript files
- Google Cloud Console

#### 3. "Backend connection failed"
**Solution:** 
- Check if backend server is running
- Verify CORS settings
- Check network requests in browser DevTools

#### 4. "Token verification failed"
**Solution:**
- Ensure `google-auth-library` is installed
- Check client ID matches in backend
- Verify token is being sent correctly

### Debug Mode
Enable debug logging:
```javascript
// Add to your component
console.log('Auth state:', { user, isAuthenticated, isLoading });
```

---

## 🔐 Security Best Practices

1. **Never expose client secret** in frontend code
2. **Always verify tokens** on the backend
3. **Use HTTPS** in production
4. **Implement token expiration** handling
5. **Validate user data** before storing
6. **Use environment variables** for sensitive data

---

## 📚 Additional Resources

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Google Sign-In for Web](https://developers.google.com/identity/gsi/web)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)

---

## 🎯 Next Steps

1. Test the implementation with your credentials
2. Customize the UI to match your design
3. Add additional user profile fields
4. Implement role-based access control
5. Add social login for other providers

**Happy coding! 🚀**
