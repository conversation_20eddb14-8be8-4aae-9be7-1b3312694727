/* Login & Registration Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Helvetica+Now+Display:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #ff6464;
    --success-color: #2ecc71;
    --error-color: #e74c3c;
    --text-color: #333;
    --light-gray: #f8f8f8;
    --medium-gray: #e0e0e0;
    --dark-gray: #666;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

html, body {
    height: 100%;
    width: 100%;
    font-family: 'Helvetica Now Display', 'Funnel Display Medium', sans-serif;
    background-color: var(--light-gray);
}

main {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    padding: 20px 0;
    
    nav {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 30px;
        
        #logo {
            img {
                height: 50px;
                object-fit: contain;
            }
        }
    }
}

.auth-container {
    display: flex;
    flex: 1;
    height: calc(100vh - 90px);
    
    .form-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 40px;
        background-color: var(--white);
        
        .form-tabs {
            display: flex;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            
            .tab-btn {
                padding: 12px 30px;
                background-color: var(--white);
                border: none;
                font-size: 16px;
                font-weight: 600;
                color: var(--dark-gray);
                cursor: pointer;
                transition: var(--transition);
                
                &.active {
                    background-color: var(--primary-color);
                    color: var(--white);
                }
                
                &:hover:not(.active) {
                    background-color: var(--light-gray);
                }
            }
        }
        
        .form-content {
            width: 100%;
            max-width: 450px;
            display: none;
            
            &.active {
                display: block;
                animation: fadeIn 0.5s ease;
            }
            
            h2 {
                font-size: 28px;
                margin-bottom: 10px;
                color: var(--secondary-color);
                text-align: center;
            }
            
            .form-subtitle {
                text-align: center;
                color: var(--dark-gray);
                margin-bottom: 30px;
                font-size: 16px;
            }
            
            form {
                .form-group {
                    margin-bottom: 20px;
                    
                    label {
                        display: block;
                        margin-bottom: 8px;
                        font-weight: 500;
                        color: var(--text-color);
                    }
                    
                    .input-with-icon {
                        position: relative;
                        
                        i {
                            position: absolute;
                            left: 15px;
                            top: 50%;
                            transform: translateY(-50%);
                            color: var(--dark-gray);
                        }
                        
                        .toggle-password {
                            left: auto;
                            right: 15px;
                            cursor: pointer;
                        }
                        
                        input {
                            width: 100%;
                            padding: 15px 15px 15px 45px;
                            border: 1px solid var(--medium-gray);
                            border-radius: 8px;
                            font-size: 16px;
                            transition: var(--transition);
                            
                            &:focus {
                                border-color: var(--primary-color);
                                box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
                                outline: none;
                            }
                            
                            &.error {
                                border-color: var(--error-color);
                            }
                        }
                    }
                    
                    .error-message {
                        color: var(--error-color);
                        font-size: 14px;
                        margin-top: 5px;
                        display: block;
                    }
                }
                
                .form-options {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 25px;
                    
                    .remember-me, .terms-conditions {
                        display: flex;
                        align-items: center;
                        
                        input[type="checkbox"] {
                            margin-right: 8px;
                        }
                        
                        label {
                            font-size: 14px;
                            color: var(--dark-gray);
                            
                            a {
                                color: var(--primary-color);
                                text-decoration: none;
                                
                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                        }
                    }
                    
                    .forgot-password {
                        font-size: 14px;
                        color: var(--primary-color);
                        text-decoration: none;
                        
                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
                
                .auth-btn {
                    width: 100%;
                    padding: 15px;
                    background-color: var(--primary-color);
                    color: var(--white);
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: var(--transition);
                    
                    &:hover {
                        background-color: darken(#3498db, 10%);
                        transform: translateY(-2px);
                    }
                    
                    &:active {
                        transform: translateY(0);
                    }
                }
            }
        }
    }
    
    .auth-image {
        flex: 1;
        background-image: url('../../../assets/images/nike-airForce.png');
        background-size: cover;
        background-position: center;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.8), rgba(44, 62, 80, 0.9));
        }
        
        .auth-content {
            position: relative;
            z-index: 1;
            text-align: center;
            color: var(--white);
            padding: 0 40px;
            
            h1 {
                font-size: 42px;
                margin-bottom: 20px;
                font-weight: 700;
            }
            
            p {
                font-size: 18px;
                max-width: 500px;
                line-height: 1.6;
            }
        }
    }
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    transform: translateY(100px);
    opacity: 0;
    z-index: 1000;
    
    &.success {
        border-left: 4px solid var(--success-color);
        
        .toast-icon {
            color: var(--success-color);
        }
    }
    
    &.error {
        border-left: 4px solid var(--error-color);
        
        .toast-icon {
            color: var(--error-color);
        }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        
        .toast-icon {
            font-size: 24px;
            margin-right: 15px;
        }
        
        .toast-message {
            font-size: 16px;
            color: var(--text-color);
        }
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .auth-container {
        flex-direction: column;
        height: auto;
        
        .auth-image {
            min-height: 300px;
            order: -1;
        }
    }
}

@media (max-width: 576px) {
    .auth-container {
        .form-container {
            padding: 30px 20px;
        }
        
        .auth-image {
            min-height: 200px;
            
            .auth-content {
                h1 {
                    font-size: 32px;
                }
                
                p {
                    font-size: 16px;
                }
            }
        }
    }
}
