/**
 * Authentication Middleware for StepStyle
 * Handles user authentication state across all pages
 */

class AuthMiddleware {
    constructor() {
        this.backendUrl = 'http://localhost:5000/api/user';
        this.loginUrl = '/Pages/login.html';
        this.homeUrl = '/index.html';
        this.currentPage = window.location.pathname;
        this.init();
    }

    async init() {
        // Check authentication status
        const isAuthenticated = await this.checkAuthStatus();
        
        // Handle page access based on authentication
        this.handlePageAccess(isAuthenticated);
        
        // Setup logout functionality
        this.setupLogoutHandlers();
        
        // Update UI based on auth status
        this.updateUI(isAuthenticated);
    }

    // Check if user is authenticated
    async checkAuthStatus() {
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');
        
        if (!token || !userData) {
            return false;
        }

        try {
            // Verify token with backend
            const response = await fetch(`${this.backendUrl}/me`, {
                headers: {
                    'Authorization': `Bear<PERSON> ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    // Update user data if needed
                    localStorage.setItem('userData', JSON.stringify(data.user));
                    return true;
                }
            }
        } catch (error) {
            console.log('Auth verification failed:', error);
        }

        // Token is invalid, clear storage
        this.clearAuthData();
        return false;
    }

    // Handle page access based on authentication
    handlePageAccess(isAuthenticated) {
        const isLoginPage = this.currentPage.includes('login.html');
        const isHomePage = this.currentPage.includes('index.html') || this.currentPage === '/';
        
        if (!isAuthenticated && !isLoginPage && !isHomePage) {
            // User not authenticated and trying to access protected page
            this.redirectToLogin();
        } else if (isAuthenticated && isLoginPage) {
            // User already authenticated but on login page
            this.redirectToHome();
        }
    }

    // Redirect to login page
    redirectToLogin() {
        this.showNotification('Please login to access this page', 'info');
        setTimeout(() => {
            window.location.href = this.loginUrl;
        }, 1500);
    }

    // Redirect to home page
    redirectToHome() {
        const userData = JSON.parse(localStorage.getItem('userData'));
        this.showNotification(`Welcome back ${userData.name}!`, 'success');
        setTimeout(() => {
            window.location.href = this.homeUrl;
        }, 1500);
    }

    // Setup logout handlers
    setupLogoutHandlers() {
        // Logout buttons
        const logoutBtns = document.querySelectorAll('.logout-btn, [data-action="logout"]');
        logoutBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        });

        // User dropdown logout
        const userDropdown = document.querySelector('.user-dropdown');
        if (userDropdown) {
            const logoutOption = userDropdown.querySelector('[data-action="logout"]');
            if (logoutOption) {
                logoutOption.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.logout();
                });
            }
        }
    }

    // Update UI based on authentication status
    updateUI(isAuthenticated) {
        const userData = isAuthenticated ? JSON.parse(localStorage.getItem('userData')) : null;
        
        // Update navbar
        this.updateNavbar(isAuthenticated, userData);
        
        // Update user info displays
        this.updateUserInfo(isAuthenticated, userData);
    }

    // Update navbar based on auth status
    updateNavbar(isAuthenticated, userData) {
        const loginBtns = document.querySelectorAll('.login-btn-nav, .signup-btn-nav');
        const userIcon = document.querySelector('.user-icon');
        const userDropdown = document.querySelector('.user-dropdown');
        
        if (isAuthenticated && userData) {
            // Hide login/signup buttons
            loginBtns.forEach(btn => {
                if (btn) btn.style.display = 'none';
            });
            
            // Show user icon
            if (userIcon) {
                userIcon.style.display = 'block';
                
                // Update user info in dropdown
                const userName = userIcon.querySelector('.user-name');
                const userEmail = userIcon.querySelector('.user-email');
                const userAvatar = userIcon.querySelector('.user-avatar');
                
                if (userName) userName.textContent = userData.name;
                if (userEmail) userEmail.textContent = userData.email;
                if (userAvatar && userData.profilePicture) {
                    userAvatar.src = userData.profilePicture;
                }
            }
        } else {
            // Show login/signup buttons
            loginBtns.forEach(btn => {
                if (btn) btn.style.display = 'block';
            });
            
            // Hide user icon
            if (userIcon) userIcon.style.display = 'none';
        }
    }

    // Update user info displays
    updateUserInfo(isAuthenticated, userData) {
        const userNameElements = document.querySelectorAll('.user-name-display');
        const userEmailElements = document.querySelectorAll('.user-email-display');
        
        if (isAuthenticated && userData) {
            userNameElements.forEach(el => {
                if (el) el.textContent = userData.name;
            });
            
            userEmailElements.forEach(el => {
                if (el) el.textContent = userData.email;
            });
        }
    }

    // Logout user
    logout() {
        // Clear authentication data
        this.clearAuthData();
        
        // Disable Google auto-select
        if (typeof google !== 'undefined' && google.accounts) {
            google.accounts.id.disableAutoSelect();
        }
        
        this.showNotification('Logged out successfully!', 'success');
        
        // Redirect to login page
        setTimeout(() => {
            window.location.href = this.loginUrl;
        }, 1500);
    }

    // Clear authentication data
    clearAuthData() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Try to use existing toast notification
        const existingToast = document.querySelector('.toast-notification');
        if (existingToast) {
            this.updateToast(existingToast, message, type);
            return;
        }
        
        // Create new notification if none exists
        const toast = document.createElement('div');
        toast.className = 'auth-notification';
        toast.innerHTML = `
            <div class="notification-content">
                <i class="notification-icon"></i>
                <span class="notification-message">${message}</span>
            </div>
        `;
        
        // Add styles
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-family: 'Helvetica Now Display', sans-serif;
            font-size: 14px;
            max-width: 300px;
        `;
        
        document.body.appendChild(toast);
        
        // Show notification
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // Hide notification
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // Update existing toast
    updateToast(toast, message, type) {
        const messageEl = toast.querySelector('.toast-message');
        const iconEl = toast.querySelector('.toast-icon');
        
        if (messageEl) messageEl.textContent = message;
        
        // Update icon and color based on type
        if (iconEl) {
            if (type === 'success') {
                iconEl.className = 'ri-check-line toast-icon';
                toast.style.background = '#27ae60';
            } else if (type === 'error') {
                iconEl.className = 'ri-error-warning-line toast-icon';
                toast.style.background = '#e74c3c';
            } else {
                iconEl.className = 'ri-information-line toast-icon';
                toast.style.background = '#3498db';
            }
        }
        
        // Show toast
        toast.classList.add('show');
        
        // Hide after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // Get current user data
    getCurrentUser() {
        const userData = localStorage.getItem('userData');
        return userData ? JSON.parse(userData) : null;
    }

    // Check if user is authenticated (public method)
    isAuthenticated() {
        return !!localStorage.getItem('authToken');
    }
}

// Initialize auth middleware when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.authMiddleware = new AuthMiddleware();
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthMiddleware;
}
