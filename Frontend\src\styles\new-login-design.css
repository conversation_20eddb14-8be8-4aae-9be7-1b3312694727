/* Modern Login Page Styles - Based on Design */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Now Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    display: flex;
    width: 100%;
    max-width: 1000px;
    min-height: 600px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Left Side - Image */
.left-side {
    flex: 1;
    background: linear-gradient(135deg, #4a4a4a 0%, #2c2c2c 100%);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px;
    color: white;
}

.brand-header {
    text-align: left;
}

.brand-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: white;
}

.shoe-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
}

.shoe-image img {
    width: 280px;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

.brand-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 3;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.brand-logo img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.brand-details h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.brand-details p {
    font-size: 14px;
    opacity: 0.8;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Right Side - Forms */
.right-side {
    flex: 1;
    padding: 60px 50px;
    display: flex;
    flex-direction: column;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.form-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: 2px;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #666;
    cursor: pointer;
}

.form-content {
    display: none;
    flex: 1;
}

.form-content.active {
    display: block;
}

.form-content h2 {
    font-size: 32px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
}

.form-subtitle {
    color: #666;
    margin-bottom: 40px;
    font-size: 16px;
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    background: #f0f2f5;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 18px;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    margin-top: 8px;
    display: inline-block;
}

.forgot-password:hover {
    text-decoration: underline;
}

.google-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    background: white;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 16px;
    width: 100%;
}

.google-btn:hover {
    background: #f8f9fa;
    border-color: #d1d5db;
}

.google-btn img {
    width: 20px;
    height: 20px;
}

.login-btn,
.create-btn {
    width: 100%;
    padding: 16px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover,
.create-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
}

.switch-form {
    text-align: center;
    color: #666;
    margin-top: 24px;
}

.switch-link {
    color: #e74c3c;
    text-decoration: none;
    font-weight: 500;
}

.switch-link:hover {
    text-decoration: underline;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 24px 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-group label {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.checkbox-group a {
    color: #667eea;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #27ae60;
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-icon {
    font-size: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 0;
    }

    .login-container {
        flex-direction: column;
        max-width: 100%;
        min-height: 100vh;
        border-radius: 0;
        box-shadow: none;
    }

    .left-side {
        min-height: 300px;
        padding: 30px;
    }

    .shoe-image {
        position: relative;
        top: auto;
        left: auto;
        transform: none;
        margin: 20px 0;
    }

    .shoe-image img {
        width: 200px;
    }

    .right-side {
        padding: 40px 30px;
    }

    .form-header h1 {
        font-size: 24px;
    }

    .form-content h2 {
        font-size: 28px;
    }

    .form-group input {
        padding: 14px 16px;
    }

    .google-btn,
    .login-btn,
    .create-btn {
        padding: 14px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .left-side {
        padding: 20px;
        min-height: 250px;
    }

    .right-side {
        padding: 30px 20px;
    }

    .form-header h1 {
        font-size: 20px;
        letter-spacing: 1px;
    }

    .form-content h2 {
        font-size: 24px;
    }

    .form-subtitle {
        font-size: 14px;
    }

    .shoe-image img {
        width: 150px;
    }

    .brand-logo {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .action-buttons {
        margin-top: 10px;
    }
}
