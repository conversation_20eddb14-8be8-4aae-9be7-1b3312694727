import { useState, useEffect, useCallback } from 'react';

/**
 * Custom React Hook for Authentication
 * Manages Google OAuth state and provides auth methods
 */

const useAuth = () => {
    const [user, setUser] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [error, setError] = useState(null);

    // Configuration
    const CLIENT_ID = '142140028318-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com';
    const BACKEND_URL = 'http://localhost:5000/api/user';

    /**
     * Initialize authentication state
     */
    useEffect(() => {
        checkAuthState();
    }, []);

    /**
     * Check if user is already authenticated
     */
    const checkAuthState = useCallback(() => {
        try {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');

            if (token && userData) {
                const parsedUser = JSON.parse(userData);
                setUser(parsedUser);
                setIsAuthenticated(true);
                console.log('✅ User already authenticated:', parsedUser.name);
            }
        } catch (error) {
            console.error('❌ Invalid stored user data:', error);
            clearAuthData();
        } finally {
            setIsLoading(false);
        }
    }, []);

    /**
     * Clear authentication data
     */
    const clearAuthData = useCallback(() => {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        setUser(null);
        setIsAuthenticated(false);
        setError(null);
    }, []);

    /**
     * Login with Google
     */
    const loginWithGoogle = useCallback(async (credential) => {
        try {
            setIsLoading(true);
            setError(null);

            console.log('🔄 Verifying Google credential...');

            // Send token to backend for verification
            const response = await fetch(`${BACKEND_URL}/google-login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token: credential })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Authentication failed');
            }

            // Store authentication data
            localStorage.setItem('authToken', data.token);
            localStorage.setItem('userData', JSON.stringify(data.user));

            // Update state
            setUser(data.user);
            setIsAuthenticated(true);

            console.log('✅ Login successful:', data.user.name);
            return { success: true, user: data.user, token: data.token };

        } catch (error) {
            console.error('❌ Login error:', error);
            setError(error.message);
            clearAuthData();
            return { success: false, error: error.message };
        } finally {
            setIsLoading(false);
        }
    }, [BACKEND_URL, clearAuthData]);

    /**
     * Logout user
     */
    const logout = useCallback(() => {
        try {
            // Disable Google auto-select
            if (window.google && window.google.accounts) {
                window.google.accounts.id.disableAutoSelect();
            }

            // Clear authentication data
            clearAuthData();

            console.log('✅ User logged out successfully');
            return { success: true };

        } catch (error) {
            console.error('❌ Logout error:', error);
            return { success: false, error: error.message };
        }
    }, [clearAuthData]);

    /**
     * Get authentication token
     */
    const getToken = useCallback(() => {
        return localStorage.getItem('authToken');
    }, []);

    /**
     * Check if token is valid (basic check)
     */
    const isTokenValid = useCallback(() => {
        const token = getToken();
        if (!token) return false;

        try {
            // Basic JWT structure check
            const parts = token.split('.');
            if (parts.length !== 3) return false;

            // Decode payload to check expiration
            const payload = JSON.parse(atob(parts[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            
            return payload.exp > currentTime;
        } catch (error) {
            console.error('❌ Token validation error:', error);
            return false;
        }
    }, [getToken]);

    /**
     * Refresh authentication state
     */
    const refreshAuth = useCallback(() => {
        if (isTokenValid()) {
            checkAuthState();
        } else {
            clearAuthData();
        }
    }, [isTokenValid, checkAuthState, clearAuthData]);

    /**
     * Make authenticated API request
     */
    const authenticatedFetch = useCallback(async (url, options = {}) => {
        const token = getToken();
        
        if (!token) {
            throw new Error('No authentication token available');
        }

        const authOptions = {
            ...options,
            headers: {
                ...options.headers,
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };

        const response = await fetch(url, authOptions);

        // Handle token expiration
        if (response.status === 401) {
            clearAuthData();
            throw new Error('Authentication expired. Please login again.');
        }

        return response;
    }, [getToken, clearAuthData]);

    /**
     * Update user profile
     */
    const updateUserProfile = useCallback(async (updates) => {
        try {
            setIsLoading(true);

            const response = await authenticatedFetch(`${BACKEND_URL}/profile`, {
                method: 'PUT',
                body: JSON.stringify(updates)
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Profile update failed');
            }

            // Update local user data
            const updatedUser = { ...user, ...data.user };
            setUser(updatedUser);
            localStorage.setItem('userData', JSON.stringify(updatedUser));

            return { success: true, user: updatedUser };

        } catch (error) {
            console.error('❌ Profile update error:', error);
            setError(error.message);
            return { success: false, error: error.message };
        } finally {
            setIsLoading(false);
        }
    }, [user, authenticatedFetch, BACKEND_URL]);

    return {
        // State
        user,
        isLoading,
        isAuthenticated,
        error,

        // Methods
        loginWithGoogle,
        logout,
        refreshAuth,
        updateUserProfile,
        
        // Utilities
        getToken,
        isTokenValid,
        authenticatedFetch,
        clearAuthData,

        // Configuration
        CLIENT_ID,
        BACKEND_URL
    };
};

export default useAuth;
