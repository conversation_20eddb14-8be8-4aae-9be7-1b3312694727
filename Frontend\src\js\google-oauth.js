/**
 * Google OAuth Implementation for StepStyle
 * React-style Google Sign-In functionality with token handling
 */

class GoogleOAuth {
    constructor() {
        this.clientId = '142140028318-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com';
        this.apiKey = 'AIzaSyAVhYVCyjVM0C1NJhzuMlmi-K7IoZB73yw';
        this.backendUrl = 'http://localhost:5000/api/user'; // Adjust according to your backend URL
        this.isInitialized = false;
        this.init();
    }

    /**
     * Initialize Google OAuth
     */
    async init() {
        try {
            // Wait for Google API to load
            await this.waitForGoogleAPI();
            
            // Initialize Google Sign-In
            await google.accounts.id.initialize({
                client_id: this.clientId,
                callback: this.handleCredentialResponse.bind(this),
                auto_select: false,
                cancel_on_tap_outside: true
            });

            this.isInitialized = true;
            console.log('Google OAuth initialized successfully');
            
            // Setup login buttons
            this.setupLoginButtons();
            
        } catch (error) {
            console.error('Failed to initialize Google OAuth:', error);
        }
    }

    /**
     * Wait for Google API to be available
     */
    waitForGoogleAPI() {
        return new Promise((resolve, reject) => {
            const checkGoogle = () => {
                if (typeof google !== 'undefined' && google.accounts) {
                    resolve();
                } else {
                    setTimeout(checkGoogle, 100);
                }
            };
            checkGoogle();
            
            // Timeout after 10 seconds
            setTimeout(() => reject(new Error('Google API failed to load')), 10000);
        });
    }

    /**
     * Setup Google login buttons
     */
    setupLoginButtons() {
        // Setup login button
        const loginBtn = document.getElementById('google-login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.signIn());
        }

        // Setup signup button
        const signupBtn = document.getElementById('google-signup-btn');
        if (signupBtn) {
            signupBtn.addEventListener('click', () => this.signIn());
        }

        // Render Google One Tap if on login page
        if (window.location.pathname.includes('login')) {
            this.renderOneTap();
        }
    }

    /**
     * Trigger Google Sign-In
     */
    signIn() {
        if (!this.isInitialized) {
            console.error('Google OAuth not initialized');
            return;
        }

        google.accounts.id.prompt((notification) => {
            if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                // Fallback to popup
                this.signInWithPopup();
            }
        });
    }

    /**
     * Sign in with popup (fallback method)
     */
    signInWithPopup() {
        // Create a temporary div for the button
        const tempDiv = document.createElement('div');
        tempDiv.style.display = 'none';
        document.body.appendChild(tempDiv);

        google.accounts.id.renderButton(tempDiv, {
            theme: 'outline',
            size: 'large',
            type: 'standard',
            text: 'signin_with',
            shape: 'rectangular',
            logo_alignment: 'left'
        });

        // Trigger click on the hidden button
        const hiddenBtn = tempDiv.querySelector('div[role="button"]');
        if (hiddenBtn) {
            hiddenBtn.click();
        }

        // Clean up
        setTimeout(() => {
            document.body.removeChild(tempDiv);
        }, 1000);
    }

    /**
     * Render Google One Tap
     */
    renderOneTap() {
        google.accounts.id.prompt();
    }

    /**
     * Handle credential response from Google
     */
    async handleCredentialResponse(response) {
        try {
            console.log('Google credential received');
            
            // Show loading state
            this.showLoading(true);
            
            // Send token to backend for verification
            const result = await this.verifyTokenWithBackend(response.credential);
            
            if (result.success) {
                // Store token and user data
                localStorage.setItem('authToken', result.token);
                localStorage.setItem('userData', JSON.stringify(result.user));
                
                // Show success message
                this.showNotification('Login successful! Welcome to StepStyle', 'success');
                
                // Redirect to dashboard or home page
                setTimeout(() => {
                    window.location.href = '/index.html';
                }, 1500);
                
            } else {
                throw new Error(result.message || 'Authentication failed');
            }
            
        } catch (error) {
            console.error('Google login error:', error);
            this.showNotification('Login failed. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Verify token with backend
     */
    async verifyTokenWithBackend(token) {
        try {
            const response = await fetch(`${this.backendUrl}/google-login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token })
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Backend verification failed');
            }
            
            return data;
            
        } catch (error) {
            console.error('Backend verification error:', error);
            throw error;
        }
    }

    /**
     * Show loading state
     */
    showLoading(show) {
        const loginBtn = document.getElementById('google-login-btn');
        const signupBtn = document.getElementById('google-signup-btn');
        
        [loginBtn, signupBtn].forEach(btn => {
            if (btn) {
                if (show) {
                    btn.disabled = true;
                    btn.innerHTML = `
                        <div class="loading-spinner"></div>
                        Signing in...
                    `;
                } else {
                    btn.disabled = false;
                    btn.innerHTML = btn.id.includes('login') 
                        ? `<img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" /> Login with Google`
                        : `<img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" /> Sign up with Google`;
                }
            }
        });
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        const notification = document.querySelector('.toast-notification');
        if (notification) {
            const messageEl = notification.querySelector('.toast-message');
            const iconEl = notification.querySelector('.toast-icon');
            
            if (messageEl) messageEl.textContent = message;
            
            // Update icon based on type
            if (iconEl) {
                iconEl.className = type === 'success' 
                    ? 'ri-check-line toast-icon' 
                    : 'ri-error-warning-line toast-icon';
            }
            
            // Update background color
            notification.style.background = type === 'success' ? '#27ae60' : '#e74c3c';
            
            // Show notification
            notification.classList.add('show');
            
            // Hide after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!localStorage.getItem('authToken');
    }

    /**
     * Get current user data
     */
    getCurrentUser() {
        const userData = localStorage.getItem('userData');
        return userData ? JSON.parse(userData) : null;
    }

    /**
     * Logout user
     */
    logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        google.accounts.id.disableAutoSelect();
        window.location.href = '/Pages/login.html';
    }
}

// Initialize Google OAuth when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.googleOAuth = new GoogleOAuth();
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GoogleOAuth;
}
