import mongoose from "mongoose";

const connectDB = async () => {
    try {
        // Set up MongoDB connection events
        mongoose.connection.on('connected', () => {
            console.log("MongoDB Connected Successfully");
        });

        mongoose.connection.on('error', (err) => {
            console.error(`MongoDB Connection Error: ${err.message}`);
        });

        mongoose.connection.on('disconnected', () => {
            console.log("MongoDB Disconnected");
        });

        // Connect to MongoDB
        await mongoose.connect(`${process.env.MONGODB_URI}/stepstyle`, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });

    } catch (error) {
        console.error(`MongoDB Connection Failed: ${error.message}`);
        // Exit process with failure
        process.exit(1);
    }
}

export default connectDB;