{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../src/retry.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAI1B,KAAK,UAAU,cAAc,CAAC,GAAgB;;IACnD,IAAI,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QACzD,OAAO,EAAC,WAAW,EAAE,KAAK,EAAC,CAAC;KAC7B;IACD,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IACtB,MAAM,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAC7D,MAAM,CAAC,KAAK;QACV,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IACzE,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI;QACvD,KAAK;QACL,MAAM;QACN,KAAK;QACL,SAAS;QACT,QAAQ;KACT,CAAC;IACF,MAAM,CAAC,iBAAiB;QACtB,MAAM,CAAC,iBAAiB,KAAK,SAAS,IAAI,MAAM,CAAC,iBAAiB,KAAK,IAAI;YACzE,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC;IAE/B,2DAA2D;IAC3D,kCAAkC;IAClC,MAAM,WAAW,GAAG;QAClB,0DAA0D;QAC1D,wDAAwD;QACxD,+BAA+B;QAC/B,gCAAgC;QAChC,qCAAqC;QACrC,oCAAoC;QACpC,8BAA8B;QAC9B,CAAC,GAAG,EAAE,GAAG,CAAC;QACV,CAAC,GAAG,EAAE,GAAG,CAAC;QACV,CAAC,GAAG,EAAE,GAAG,CAAC;KACX,CAAC;IACF,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI,WAAW,CAAC;IAErE,mCAAmC;IACnC,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC;IAEhC,2CAA2C;IAC3C,MAAM,aAAa,GAAG,MAAM,CAAC,WAAW,IAAI,kBAAkB,CAAC;IAC/D,IAAI,CAAC,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;QAC/B,OAAO,EAAC,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAC,CAAC;KACjD;IAED,mDAAmD;IACnD,gEAAgE;IAChE,MAAM,UAAU,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAA,MAAM,CAAC,UAAU,mCAAI,GAAG,CAAC;IAC7E,+CAA+C;IAC/C,MAAM,KAAK,GACT,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;IAE1E,kDAAkD;IAClD,GAAG,CAAC,MAAM,CAAC,WAAY,CAAC,mBAAoB,IAAI,CAAC,CAAC;IAElD,iEAAiE;IACjE,MAAM,OAAO,GAAG,MAAM,CAAC,YAAY;QACjC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;QACjC,CAAC,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACpB,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IAEP,4DAA4D;IAC5D,IAAI,MAAM,CAAC,cAAc,EAAE;QACzB,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;KAC5B;IAED,kEAAkE;IAClE,MAAM,OAAO,CAAC;IACd,OAAO,EAAC,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAC,CAAC;AACjD,CAAC;AAvED,wCAuEC;AAED;;;GAGG;AACH,SAAS,kBAAkB,CAAC,GAAgB;IAC1C,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAE9B,+CAA+C;IAC/C,6EAA6E;IAC7E,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;IAED,yDAAyD;IACzD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;QACjC,OAAO,KAAK,CAAC;KACd;IAED,kEAAkE;IAClE,IACE,CAAC,GAAG,CAAC,QAAQ;QACb,CAAC,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,iBAAkB,EAC9D;QACA,OAAO,KAAK,CAAC;KACd;IAED,0CAA0C;IAC1C,IACE,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM;QAClB,MAAM,CAAC,kBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,EACvE;QACA,OAAO,KAAK,CAAC;KACd;IAED,2DAA2D;IAC3D,kCAAkC;IAClC,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE;QACvC,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,CAAC,kBAAmB,EAAE;YACnD,MAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnC,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,GAAG,EAAE;gBAClC,SAAS,GAAG,IAAI,CAAC;gBACjB,MAAM;aACP;SACF;QACD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,KAAK,CAAC;SACd;KACF;IAED,0CAA0C;IAC1C,MAAM,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAC7D,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,KAAM,EAAE;QAC/C,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;GAGG;AACH,SAAS,SAAS,CAAC,GAAgB;IACjC,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE;QAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC;KAC/B;IACD,OAAO;AACT,CAAC"}