/**
 * New Login & Registration functionality for StepStyle website
 */

// API URL - Using the existing backend URL
const API_URL = 'http://localhost:4000/api';

// DOM Elements
let loginForm;
let registerForm;
let togglePasswordButtons;
let toast;
let tabLinks;

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    console.log('New login script loaded');
    
    // Initialize DOM elements
    initializeElements();
    
    // Set up password visibility toggle
    setupPasswordToggle();
    
    // Set up form submissions
    setupFormSubmissions();
    
    // Set up tab switching
    setupTabSwitching();
    
    // Check if user is already logged in
    checkAuthStatus();
});

/**
 * Initialize DOM elements
 */
function initializeElements() {
    loginForm = document.getElementById('login');
    registerForm = document.getElementById('register');
    togglePasswordButtons = document.querySelectorAll('.toggle-password');
    toast = document.querySelector('.toast-notification');
    tabLinks = document.querySelectorAll('.tab-link');
}

/**
 * Set up password visibility toggle
 */
function setupPasswordToggle() {
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', () => {
            const passwordInput = button.previousElementSibling;
            const isPassword = passwordInput.type === 'password';
            
            passwordInput.type = isPassword ? 'text' : 'password';
            button.className = isPassword ? 'ri-eye-off-line toggle-password' : 'ri-eye-line toggle-password';
        });
    });
}

/**
 * Set up form submissions
 */
function setupFormSubmissions() {
    // Login form submission
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            // Clear previous errors
            clearErrors();
            
            // Validate inputs
            if (!validateEmail(email)) {
                showError('login-email-error', 'Please enter a valid email address');
                return;
            }
            
            if (!password) {
                showError('login-password-error', 'Password is required');
                return;
            }
            
            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Save token to localStorage
                    localStorage.setItem('auth-token', data.token);
                    
                    // Show success message
                    showToast('Login successful! Redirecting...', 'success');
                    
                    // Redirect to home page after 1.5 seconds
                    setTimeout(() => {
                        // Check if there's a redirect URL in localStorage
                        const redirectUrl = localStorage.getItem('auth-redirect') || '../Frontend/index.html';
                        localStorage.removeItem('auth-redirect'); // Clear the redirect URL
                        window.location.href = redirectUrl;
                    }, 1500);
                } else {
                    // Show error message
                    showToast('User or email and password is wrong', 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                showToast('An error occurred. Please try again later.', 'error');
            }
        });
    }
    
    // Register form submission
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const termsAccepted = document.getElementById('terms').checked;
            
            // Clear previous errors
            clearErrors();
            
            // Validate inputs
            if (!name.trim()) {
                showError('register-name-error', 'Full name is required');
                return;
            }
            
            if (!validateEmail(email)) {
                showError('register-email-error', 'Please enter a valid email address');
                return;
            }
            
            if (password.length < 8) {
                showError('register-password-error', 'Password must be at least 8 characters long');
                return;
            }
            
            if (!termsAccepted) {
                showToast('Please accept the Terms & Conditions', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ name, email, password }),
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Save token to localStorage
                    localStorage.setItem('auth-token', data.token);
                    
                    // Show success message
                    showToast('Account created successfully! Redirecting...', 'success');
                    
                    // Redirect to home page after 1.5 seconds
                    setTimeout(() => {
                        window.location.href = '../Frontend/index.html';
                    }, 1500);
                } else {
                    // Show error message
                    showToast(data.message || 'Registration failed. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Registration error:', error);
                showToast('An error occurred. Please try again later.', 'error');
            }
        });
    }
}

/**
 * Set up tab switching between login and register
 */
function setupTabSwitching() {
    tabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            const targetTab = link.getAttribute('data-tab');
            switchTab(targetTab);
        });
    });
    
    // Check URL parameters for tab selection
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    if (tab === 'register') {
        switchTab('register');
    }
}

/**
 * Switch between login and register tabs
 */
function switchTab(tab) {
    const loginFormContent = document.getElementById('login-form');
    const registerFormContent = document.getElementById('register-form');
    
    if (tab === 'register') {
        loginFormContent.classList.remove('active');
        registerFormContent.classList.add('active');
    } else {
        registerFormContent.classList.remove('active');
        loginFormContent.classList.add('active');
    }
}

/**
 * Validate email format
 */
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Show error message for a specific field
 */
function showError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = message;
    }
}

/**
 * Clear all error messages
 */
function clearErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.textContent = '';
    });
}

/**
 * Show toast notification
 */
function showToast(message, type = 'success') {
    if (!toast) return;
    
    const toastMessage = toast.querySelector('.toast-message');
    const toastIcon = toast.querySelector('.toast-icon');
    
    // Update message
    toastMessage.textContent = message;
    
    // Update icon and color based on type
    if (type === 'error') {
        toastIcon.className = 'ri-close-line toast-icon';
        toast.style.background = '#e74c3c';
    } else {
        toastIcon.className = 'ri-check-line toast-icon';
        toast.style.background = '#2ecc71';
    }
    
    // Show toast
    toast.classList.add('show');
    
    // Hide toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

/**
 * Check if user is already authenticated
 */
function checkAuthStatus() {
    const token = localStorage.getItem('auth-token');
    
    if (token) {
        // User is already logged in, redirect to home page
        window.location.href = '../Frontend/index.html';
    }
}
