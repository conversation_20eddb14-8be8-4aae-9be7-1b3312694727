<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Google OAuth - StepStyle</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .google-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            padding: 16px;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            background: white;
            color: #333;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
            width: 100%;
        }
        .google-btn:hover {
            background: #f8f9fa;
            border-color: #d1d5db;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
        }
        .error {
            background: #ffe6e6;
            border-color: #ff9999;
            color: #cc0000;
        }
        .success {
            background: #e6ffe6;
            border-color: #99ff99;
            color: #006600;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google OAuth Test - StepStyle</h1>
        <p>Test your Google OAuth implementation here.</p>
        
        <button id="google-login-btn" class="google-btn">
            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" width="20" height="20" />
            Test Google Login
        </button>

        <button onclick="testBackend()" style="padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Test Backend Connection
        </button>

        <button onclick="logout()" style="padding: 10px 20px; margin: 10px 0; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">
            Logout
        </button>

        <div id="result"></div>
    </div>

    <script>
        const CLIENT_ID = '************-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com';
        const BACKEND_URL = 'http://localhost:5000/api/user';

        // Initialize Google OAuth
        function initializeGoogleOAuth() {
            google.accounts.id.initialize({
                client_id: CLIENT_ID,
                callback: handleCredentialResponse
            });
        }

        // Handle Google credential response
        async function handleCredentialResponse(response) {
            showResult('Processing Google login...', 'info');
            
            try {
                // Send token to backend
                const backendResponse = await fetch(`${BACKEND_URL}/google-login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token: response.credential })
                });

                const data = await backendResponse.json();

                if (data.success) {
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('userData', JSON.stringify(data.user));
                    
                    showResult(`
                        <strong>✅ Login Successful!</strong><br>
                        <strong>User:</strong> ${data.user.name}<br>
                        <strong>Email:</strong> ${data.user.email}<br>
                        <strong>Auth Provider:</strong> ${data.user.authProvider}<br>
                        <strong>Token:</strong> ${data.token.substring(0, 50)}...<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `, 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Test backend connection
        async function testBackend() {
            showResult('Testing backend connection...', 'info');
            
            try {
                const response = await fetch(`${BACKEND_URL}/google-login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token: 'test' })
                });

                const data = await response.json();
                
                if (response.status === 400 && data.message === 'Google token is required') {
                    showResult('✅ Backend is running and responding correctly!', 'success');
                } else {
                    showResult(`✅ Backend responded: ${JSON.stringify(data)}`, 'success');
                }
            } catch (error) {
                showResult(`❌ Backend connection failed: ${error.message}`, 'error');
            }
        }

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userData');
            google.accounts.id.disableAutoSelect();
            showResult('✅ Logged out successfully!', 'success');
        }

        // Show result
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
        }

        // Setup login button
        document.getElementById('google-login-btn').addEventListener('click', () => {
            google.accounts.id.prompt();
        });

        // Initialize when page loads
        window.addEventListener('load', () => {
            setTimeout(initializeGoogleOAuth, 1000);
        });

        // Check if user is already logged in
        window.addEventListener('load', () => {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');
            
            if (token && userData) {
                const user = JSON.parse(userData);
                showResult(`
                    <strong>🔐 Already logged in!</strong><br>
                    <strong>User:</strong> ${user.name}<br>
                    <strong>Email:</strong> ${user.email}<br>
                    <strong>Token:</strong> ${token.substring(0, 50)}...
                `, 'success');
            }
        });
    </script>
</body>
</html>
