{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "server": "nodemon server.js"}, "author": "", "license": "ISC", "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.6.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "nodemon": "^3.1.9", "razorpay": "^2.9.6", "stripe": "^18.1.0", "validator": "^13.15.0"}}