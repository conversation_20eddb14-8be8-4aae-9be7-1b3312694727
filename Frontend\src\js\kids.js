// Import shared functionality
import { initCart, addToCart } from './cart';
import { sharedProducts, findProductById } from './shared-products';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Lenis from 'lenis';
import { checkAuth } from './auth-check.js';

const lenis = new Lenis();

// Use requestAnimationFrame to continuously update the scroll
function raf(time) {
  lenis.raf(time);
  requestAnimationFrame(raf);
}
requestAnimationFrame(raf);
// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Filter kids products from shared products
const kidsProducts = sharedProducts.filter(product => product.id.startsWith('k'));

// Use the shared products for kids
// We already added the kids products to shared-products.js, so we don't need fallback data

// Initialize cart immediately to ensure it works when navigating between pages
initCart();

// Note: We're using manually added product cards in the HTML instead of generating them dynamically

// Initialize the page
function initializePage() {
  // Initialize cart functionality
  initCart();

  // We've already added the product cards manually in the HTML, so we don't need to populate them here
  // But we still need to add event listeners to them

  // Add click event listeners for "Add to Cart" buttons
  document.querySelectorAll('.add-to-cart-btn').forEach(button => {
    button.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent card click event
      const productId = button.dataset.productId;
      const product = kidsProducts.find(p => p.id === productId);
      if (product) {
        // Only open cart when adding from product details
        const isInProductDetails = button.closest('#product-details') !== null;
        addToCart(product, isInProductDetails);
      }
    });
  });

  // Product card click handler for details view
  document.querySelectorAll('.product-card').forEach(card => {
    card.addEventListener('click', (e) => {
      if (!e.target.classList.contains('add-to-cart-btn')) {
        const productId = card.dataset.productId;
        showProductDetails(productId);
      }
    });
  });

  // Add event listener for "Back to Collection" button
  const backToCollectionBtn = document.querySelector('.back-to-collection-btn');
  if (backToCollectionBtn) {
    backToCollectionBtn.addEventListener('click', () => {
      const productDetails = document.getElementById('product-details');
      productDetails.classList.remove('active');

      // Scroll to collection section
      const collectionSection = document.getElementById('kids-collection');
      collectionSection.scrollIntoView({ behavior: 'smooth' });
    });
  }

  // Setup review carousel
  setupReviewCarousel();

  // Setup size selection
  setupSizeSelection();

  // Check URL parameters for product ID
  const urlParams = new URLSearchParams(window.location.search);
  const productId = urlParams.get('product');
  if (productId) {
    showProductDetails(productId);
  }

  // Setup "Shop Now" button in hero section
  const shopNowBtn = document.querySelector('.shop-now-btn');
  if (shopNowBtn) {
    shopNowBtn.addEventListener('click', () => {
      const kidsCollectionSection = document.getElementById('kids-collection');
      kidsCollectionSection.scrollIntoView({ behavior: 'smooth' });
    });
  }
}

// Function to show product details
function showProductDetails(productId) {
  // First try to find the product in our shared products
  const product = findProductById(productId);

  // For our manually added product cards, we'll use a simpler approach
  // Get the product card that was clicked
  const productCard = document.querySelector(`.product-card[data-product-id="${productId}"]`);
  if (!productCard && !product) return;

  // Get elements
  const productDetails = document.getElementById('product-details');
  const mainImage = document.getElementById('main-product-image');
  const productTitle = document.querySelector('#product-details .product-title');
  const originalPrice = document.querySelector('#product-details .original-price');
  const currentPrice = document.querySelector('#product-details .current-price');
  const discountBadge = document.querySelector('#product-details .discount-badge');
  const addToCartBtn = document.querySelector('#product-details .add-to-cart-btn');

  // Get data from the product card
  const cardImage = productCard.querySelector('.product-image img');
  const cardName = productCard.querySelector('.product-name');
  const cardOriginalPrice = productCard.querySelector('.original-price');
  const cardCurrentPrice = productCard.querySelector('.current-price');
  const cardBadge = productCard.querySelector('.product-badge');

  // Update product details
  if (cardImage) {
    mainImage.src = cardImage.src;
    mainImage.alt = cardImage.alt;
  }

  if (cardName) {
    productTitle.textContent = cardName.textContent;
  }

  if (cardOriginalPrice) {
    originalPrice.textContent = cardOriginalPrice.textContent;
  }

  if (cardCurrentPrice) {
    currentPrice.textContent = cardCurrentPrice.textContent;
  }

  if (cardBadge) {
    // Extract the percentage from the badge (e.g., "18% OFF" -> "-18%")
    const percentage = cardBadge.textContent.match(/\d+/);
    if (percentage) {
      discountBadge.textContent = `-${percentage[0]}%`;
    }
  }

  // Update "Add to Cart" button
  addToCartBtn.dataset.productId = productId;

  // Update thumbnails - use the same image for all thumbnails for now
  const thumbnails = document.querySelectorAll('.thumbnail');
  thumbnails.forEach((thumbnail, index) => {
    // Remove existing event listeners by cloning and replacing
    const newThumbnail = thumbnail.cloneNode(true);
    thumbnail.parentNode.replaceChild(newThumbnail, thumbnail);

    if (index === 0 && cardImage) {
      newThumbnail.src = cardImage.src;
      newThumbnail.classList.add('active');
    }

    // Add click event to thumbnail
    newThumbnail.addEventListener('click', () => {
      // Remove active class from all thumbnails
      thumbnails.forEach(t => t.classList.remove('active'));

      // Add active class to clicked thumbnail
      newThumbnail.classList.add('active');

      // Update main image
      mainImage.src = newThumbnail.src;
    });
  });

  // Show product details section
  productDetails.classList.add('active');
  productDetails.scrollIntoView({ behavior: 'smooth' });

  // Update URL with product ID without reloading the page
  const url = new URL(window.location);
  url.searchParams.set('product', productId);
  window.history.pushState({}, '', url);
}

// Function to handle review carousel
function setupReviewCarousel() {
  const slides = document.querySelectorAll('.review-slide');
  const dots = document.querySelectorAll('.carousel-dots .dot');
  const prevBtn = document.querySelector('.prev-btn');
  const nextBtn = document.querySelector('.next-btn');

  if (!slides.length || !dots.length) return;

  let currentSlide = 0;
  const totalSlides = slides.length;

  // Function to show a specific slide
  function showSlide(index) {
    // Hide all slides
    slides.forEach(slide => {
      slide.classList.remove('active');
    });

    // Remove active class from all dots
    dots.forEach(dot => {
      dot.classList.remove('active');
    });

    // Show the current slide and activate the corresponding dot
    slides[index].classList.add('active');
    dots[index].classList.add('active');

    // Update current slide index
    currentSlide = index;
  }

  // Event listener for previous button
  if (prevBtn) {
    prevBtn.addEventListener('click', () => {
      let newIndex = currentSlide - 1;
      if (newIndex < 0) {
        newIndex = totalSlides - 1;
      }
      showSlide(newIndex);
    });
  }

  // Event listener for next button
  if (nextBtn) {
    nextBtn.addEventListener('click', () => {
      let newIndex = currentSlide + 1;
      if (newIndex >= totalSlides) {
        newIndex = 0;
      }
      showSlide(newIndex);
    });
  }

  // Event listeners for dots
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      showSlide(index);
    });
  });

  // Auto-advance slides every 5 seconds
  setInterval(() => {
    let newIndex = currentSlide + 1;
    if (newIndex >= totalSlides) {
      newIndex = 0;
    }
    showSlide(newIndex);
  }, 5000);
}

// Function to handle size selection in product details
function setupSizeSelection() {
  const sizeButtons = document.querySelectorAll('.size-btn');

  sizeButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all size buttons
      sizeButtons.forEach(btn => btn.classList.remove('active'));

      // Add active class to clicked button
      button.classList.add('active');
    });
  });
}

// Add animation to product cards
function animateProductCards() {
  const cards = document.querySelectorAll('.product-card');

  cards.forEach((card, index) => {
    // Add staggered animation delay
    card.style.animationDelay = `${index * 0.1}s`;
    card.classList.add('animate');
  });
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Check if user is authenticated
  checkAuth();

  initializePage();
  animateProductCards();
  setupSizeSelection();

  // Check if there's a product ID in the URL and show that product
  const urlParams = new URLSearchParams(window.location.search);
  const productId = urlParams.get('product');
  if (productId) {
    showProductDetails(productId);
  }

  // Note: User icon click event is now handled in auth-ui.js
});
// Navbar scroll feature
function navBarScrollAnimation() {
  let lastScrollTop = 0;
  window.addEventListener("scroll", function() {
    let navbar = document.querySelector("nav");
    let currentScroll = window.pageYOffset;
    if (currentScroll > lastScrollTop && currentScroll > 100) {
      navbar.style.top = "-90px"; // Hide navbar
    } else {
      navbar.style.top = "0"; // Show navbar
    }
    lastScrollTop = currentScroll;
  });
}
navBarScrollAnimation();