import React, { useState, useEffect } from 'react';

/**
 * React Google Login Component
 * Complete implementation for Google OAuth in React
 */

const GoogleLoginButton = ({ onSuccess, onError, buttonText = "Sign in with Google" }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
    
    // Your new Google OAuth credentials
    const CLIENT_ID = '************-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com';
    const BACKEND_URL = 'http://localhost:5000/api/user';

    useEffect(() => {
        // Load Google API script
        const loadGoogleAPI = () => {
            if (window.google) {
                initializeGoogleOAuth();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://accounts.google.com/gsi/client';
            script.async = true;
            script.defer = true;
            script.onload = initializeGoogleOAuth;
            document.head.appendChild(script);
        };

        const initializeGoogleOAuth = () => {
            if (window.google && window.google.accounts) {
                window.google.accounts.id.initialize({
                    client_id: CLIENT_ID,
                    callback: handleCredentialResponse,
                    auto_select: false,
                    cancel_on_tap_outside: true
                });
                setIsGoogleLoaded(true);
                console.log('✅ Google OAuth initialized');
            }
        };

        loadGoogleAPI();
    }, []);

    /**
     * Handle Google credential response
     */
    const handleCredentialResponse = async (response) => {
        try {
            setIsLoading(true);
            console.log('🔑 Google credential received');

            // Send token to backend for verification
            const result = await verifyTokenWithBackend(response.credential);

            if (result.success) {
                // Store authentication data
                localStorage.setItem('authToken', result.token);
                localStorage.setItem('userData', JSON.stringify(result.user));

                // Call success callback
                if (onSuccess) {
                    onSuccess(result);
                }

                console.log('✅ Login successful:', result.user.name);
            } else {
                throw new Error(result.message || 'Authentication failed');
            }

        } catch (error) {
            console.error('❌ Google login error:', error);
            if (onError) {
                onError(error);
            }
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * Verify token with backend
     */
    const verifyTokenWithBackend = async (token) => {
        try {
            const response = await fetch(`${BACKEND_URL}/google-login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ token })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Backend verification failed');
            }

            return data;

        } catch (error) {
            throw new Error(`Backend error: ${error.message}`);
        }
    };

    /**
     * Handle button click
     */
    const handleGoogleSignIn = () => {
        if (!isGoogleLoaded) {
            console.error('❌ Google OAuth not loaded');
            return;
        }

        if (isLoading) {
            return;
        }

        // Trigger Google Sign-In
        window.google.accounts.id.prompt();
    };

    return (
        <button
            onClick={handleGoogleSignIn}
            disabled={isLoading || !isGoogleLoaded}
            className="google-login-button"
            style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                padding: '12px 24px',
                border: '1px solid #dadce0',
                borderRadius: '8px',
                backgroundColor: '#fff',
                color: '#3c4043',
                fontSize: '14px',
                fontWeight: '500',
                cursor: isLoading || !isGoogleLoaded ? 'not-allowed' : 'pointer',
                transition: 'all 0.2s ease',
                minWidth: '200px',
                opacity: isLoading || !isGoogleLoaded ? 0.6 : 1
            }}
            onMouseEnter={(e) => {
                if (!isLoading && isGoogleLoaded) {
                    e.target.style.backgroundColor = '#f8f9fa';
                    e.target.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                }
            }}
            onMouseLeave={(e) => {
                if (!isLoading && isGoogleLoaded) {
                    e.target.style.backgroundColor = '#fff';
                    e.target.style.boxShadow = 'none';
                }
            }}
        >
            {isLoading ? (
                <>
                    <div
                        style={{
                            width: '16px',
                            height: '16px',
                            border: '2px solid #f3f3f3',
                            borderTop: '2px solid #4285f4',
                            borderRadius: '50%',
                            animation: 'spin 1s linear infinite'
                        }}
                    />
                    Signing in...
                </>
            ) : (
                <>
                    <svg width="18" height="18" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    {buttonText}
                </>
            )}
        </button>
    );
};

export default GoogleLoginButton;

// CSS for spinner animation (add to your global CSS)
const spinnerCSS = `
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
`;

// Add CSS to document head
if (typeof document !== 'undefined') {
    const style = document.createElement('style');
    style.textContent = spinnerCSS;
    document.head.appendChild(style);
}
