/* Search Results Styling */

.search-results {
  position: absolute;
  top: 100%;
  right: 0;
  width: 350px;
  max-height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow-y: auto;
  display: none;
  margin-top: 5px;
}

.search-results.show {
  display: block;
  animation: fadeIn 0.2s ease-in-out;
}

.search-result-item {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: #f9f9f9;
}

.search-result-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
}

.search-result-image {
  width: 60px;
  height: 60px;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.search-result-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.search-result-info {
  flex: 1;
}

.search-result-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.search-result-gender {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #666;
}

.search-result-price {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #111;
}

.no-results {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .search-results {
    width: 100%;
    max-width: 350px;
    right: 0;
  }
}

@media (max-width: 480px) {
  .search-results {
    width: 100%;
    max-width: 100%;
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    border-radius: 0;
  }
}
