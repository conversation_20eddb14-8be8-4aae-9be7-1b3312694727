<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StepStyle - Login & Register</title>
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon" />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="../src/styles/new-login-design.css">
    <!-- Google Sign-In API -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <div class="login-container">
        <!-- Left side with image and branding -->
        <div class="left-side">
            <div class="brand-header">
                <h2>StepStyle</h2>
            </div>

            <div class="shoe-image">
                <img src="../assets/images/stepstyle-air-flex-pro.png" alt="StepStyle Air Flex Pro" />
            </div>

            <div class="brand-info">
                <div class="brand-logo">
                    <img src="../assets/images/stepstyle_inverted-removebg-preview.png" alt="StepStyle Logo" />
                    <div class="brand-details">
                        <h3>StepStyle</h3>
                        <p>Footwear & Fashion</p>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="action-btn"><i class="ri-heart-line"></i></button>
                    <button class="action-btn"><i class="ri-share-line"></i></button>
                </div>
            </div>
        </div>

        <!-- Right side with forms -->
        <div class="right-side">
            <div class="form-header">
                <h1>STEPSTYLE</h1>
                <div class="language-selector">
                    <span>EN</span>
                    <i class="ri-arrow-down-s-line"></i>
                </div>
            </div>

            <!-- Login Form -->
            <div class="form-content active" id="login-form">
                <h2>Welcome Back</h2>
                <p class="form-subtitle">Sign in to continue your shopping experience</p>

                <form id="login-form-element">
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <input type="email" id="login-email" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <div class="password-container">
                            <input type="password" id="login-password" placeholder="••••••" value="password123">
                            <button type="button" class="password-toggle">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>

                    <button type="button" class="google-btn" id="google-login-btn">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" />
                        Login with Google
                    </button>

                    <button type="submit" class="login-btn">Login</button>

                    <p class="switch-form">
                        Don't have an account? <a href="#" class="switch-link" data-target="register">Sign up</a>
                    </p>
                </form>
            </div>

            <!-- Register Form -->
            <div class="form-content" id="register-form">
                <h2>Create Account</h2>
                <p class="form-subtitle">Join StepStyle for exclusive offers and more</p>

                <form id="register-form-element">
                    <div class="form-group">
                        <label for="register-name">Full Name</label>
                        <input type="text" id="register-name" placeholder="vansh sharma" value="vansh sharma">
                    </div>

                    <div class="form-group">
                        <label for="register-email">Email</label>
                        <input type="email" id="register-email" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="register-password">Password</label>
                        <div class="password-container">
                            <input type="password" id="register-password" placeholder="••••••" value="password123">
                            <button type="button" class="password-toggle">
                                <i class="ri-eye-line"></i>
                            </button>
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="terms" checked>
                        <label for="terms">I agree to the <a href="#">Terms & Conditions</a></label>
                    </div>

                    <button type="button" class="google-btn" id="google-signup-btn">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%234285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/%3E%3Cpath fill='%2334A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/%3E%3Cpath fill='%23FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/%3E%3Cpath fill='%23EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/%3E%3C/svg%3E" alt="Google" />
                        Sign up with Google
                    </button>

                    <button type="submit" class="create-btn">Create Account</button>

                    <p class="switch-form">
                        Already have an account? <a href="#" class="switch-link" data-target="login">Sign in</a>
                    </p>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="toast-notification">
        <i class="ri-check-line toast-icon"></i>
        <span class="toast-message">Welcome to StepStyle!</span>
    </div>

    <script src="../src/js/new-login-design.js"></script>

    <!-- Complete Authentication System -->
    <script>
        // Authentication Configuration
        const AUTH_CONFIG = {
            CLIENT_ID: '************-00m8l3dj4k2vql7hb6nv0dbtcj05091e.apps.googleusercontent.com',
            BACKEND_URL: 'http://localhost:5000/api/user',
            HOME_URL: '../index.html'
        };

        // Authentication Class
        class AuthSystem {
            constructor() {
                this.isGoogleInitialized = false;
                this.init();
            }

            async init() {
                // Check if user is already logged in
                await this.checkExistingAuth();

                // Initialize Google OAuth
                this.initGoogleOAuth();

                // Setup form handlers
                this.setupFormHandlers();
            }

            // Check if user is already authenticated
            async checkExistingAuth() {
                const token = localStorage.getItem('authToken');
                const userData = localStorage.getItem('userData');

                if (token && userData) {
                    try {
                        // Verify token with backend
                        const response = await fetch(`${AUTH_CONFIG.BACKEND_URL}/me`, {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                // User is still authenticated, redirect to home
                                this.showToast(`Welcome back ${data.user.name}!`, 'success');
                                setTimeout(() => {
                                    window.location.href = AUTH_CONFIG.HOME_URL;
                                }, 1500);
                                return;
                            }
                        }
                    } catch (error) {
                        console.log('Token verification failed:', error);
                    }

                    // Token is invalid, clear storage
                    this.clearAuthData();
                }
            }

            // Setup form event handlers
            setupFormHandlers() {
                // Login form
                const loginForm = document.getElementById('login-form-element');
                if (loginForm) {
                    loginForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleLogin();
                    });
                }

                // Register form
                const registerForm = document.getElementById('register-form-element');
                if (registerForm) {
                    registerForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.handleRegister();
                    });
                }

                // Form switching
                const switchLinks = document.querySelectorAll('.switch-link');
                switchLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchForm(e.target.dataset.target);
                    });
                });

                // Password toggle
                const passwordToggles = document.querySelectorAll('.password-toggle');
                passwordToggles.forEach(toggle => {
                    toggle.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.togglePassword(toggle);
                    });
                });
            }

            // Handle login
            async handleLogin() {
                const email = document.getElementById('login-email').value.trim();
                const password = document.getElementById('login-password').value;

                this.showLoading(true, 'login');

                try {
                    const response = await fetch(`${AUTH_CONFIG.BACKEND_URL}/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.handleAuthSuccess(data);
                    } else {
                        this.showToast(data.message || 'Login failed', 'error');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showToast('Network error. Please try again.', 'error');
                } finally {
                    this.showLoading(false, 'login');
                }
            }

            // Handle registration
            async handleRegister() {
                const name = document.getElementById('register-name').value.trim();
                const email = document.getElementById('register-email').value.trim();
                const password = document.getElementById('register-password').value;
                const termsAccepted = document.getElementById('terms').checked;

                this.showLoading(true, 'register');

                try {
                    const response = await fetch(`${AUTH_CONFIG.BACKEND_URL}/register`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ name, email, password })
                    });

                    const data = await response.json();

                    if (data.success) {
                        this.handleAuthSuccess(data);
                    } else {
                        this.showToast(data.message || 'Registration failed', 'error');
                    }
                } catch (error) {
                    console.error('Registration error:', error);
                    this.showToast('Network error. Please try again.', 'error');
                } finally {
                    this.showLoading(false, 'register');
                }
            }

            // Handle successful authentication
            handleAuthSuccess(data) {
                // Store authentication data
                localStorage.setItem('authToken', data.token);
                localStorage.setItem('userData', JSON.stringify(data.user));

                this.showToast(`Welcome ${data.user.name}! Redirecting to home...`, 'success');

                // Redirect to home page
                setTimeout(() => {
                    window.location.href = AUTH_CONFIG.HOME_URL;
                }, 2000);
            }

            // Clear authentication data
            clearAuthData() {
                localStorage.removeItem('authToken');
                localStorage.removeItem('userData');
            }

            // Switch between login and register forms
            switchForm(target) {
                const loginForm = document.getElementById('login-form');
                const registerForm = document.getElementById('register-form');

                if (target === 'register') {
                    loginForm.classList.remove('active');
                    registerForm.classList.add('active');
                } else {
                    registerForm.classList.remove('active');
                    loginForm.classList.add('active');
                }
            }

            // Toggle password visibility
            togglePassword(button) {
                const passwordContainer = button.closest('.password-container');
                const passwordInput = passwordContainer.querySelector('input');
                const icon = button.querySelector('i');

                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.className = 'ri-eye-off-line';
                } else {
                    passwordInput.type = 'password';
                    icon.className = 'ri-eye-line';
                }
            }

            // Show loading state
            showLoading(show, type) {
                const loginBtn = document.querySelector('.login-btn');
                const createBtn = document.querySelector('.create-btn');
                const googleBtns = document.querySelectorAll('.google-btn');

                if (type === 'login' && loginBtn) {
                    loginBtn.disabled = show;
                    loginBtn.textContent = show ? 'Signing in...' : 'Login';
                } else if (type === 'register' && createBtn) {
                    createBtn.disabled = show;
                    createBtn.textContent = show ? 'Creating Account...' : 'Create Account';
                }

                // Disable Google buttons during loading
                googleBtns.forEach(btn => {
                    btn.disabled = show;
                    btn.style.opacity = show ? '0.6' : '1';
                });
            }

            // Show toast notification
            showToast(message, type = 'info') {
                const toast = document.querySelector('.toast-notification');
                if (toast) {
                    const messageEl = toast.querySelector('.toast-message');
                    const iconEl = toast.querySelector('.toast-icon');

                    if (messageEl) messageEl.textContent = message;

                    // Update icon and color based on type
                    if (iconEl) {
                        if (type === 'success') {
                            iconEl.className = 'ri-check-line toast-icon';
                            toast.style.background = '#27ae60';
                        } else if (type === 'error') {
                            iconEl.className = 'ri-error-warning-line toast-icon';
                            toast.style.background = '#e74c3c';
                        } else {
                            iconEl.className = 'ri-information-line toast-icon';
                            toast.style.background = '#3498db';
                        }
                    }

                    // Show toast
                    toast.classList.add('show');

                    // Hide after 4 seconds
                    setTimeout(() => {
                        toast.classList.remove('show');
                    }, 4000);
                }
            }

            // Initialize Google OAuth
            initGoogleOAuth() {
                if (typeof google !== 'undefined' && google.accounts) {
                    try {
                        google.accounts.id.initialize({
                            client_id: AUTH_CONFIG.CLIENT_ID,
                            callback: this.handleGoogleResponse.bind(this),
                            auto_select: false,
                            cancel_on_tap_outside: true
                        });

                        this.isGoogleInitialized = true;
                        console.log('✅ Google OAuth initialized');
                        this.setupGoogleButtons();
                    } catch (error) {
                        console.error('Google OAuth initialization failed:', error);
                    }
                } else {
                    setTimeout(() => this.initGoogleOAuth(), 500);
                }
            }

            // Setup Google login buttons
            setupGoogleButtons() {
                const loginBtn = document.getElementById('google-login-btn');
                const signupBtn = document.getElementById('google-signup-btn');

                if (loginBtn) {
                    loginBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.triggerGoogleLogin();
                    });
                }

                if (signupBtn) {
                    signupBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.triggerGoogleLogin();
                    });
                }
            }

            // Trigger Google Login
            triggerGoogleLogin() {
                if (!this.isGoogleInitialized) {
                    this.showToast('Google Sign-In not ready. Please try again.', 'error');
                    return;
                }

                this.showLoading(true, 'google');

                google.accounts.id.prompt((notification) => {
                    if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
                        this.showLoading(false, 'google');
                        this.showToast('Google Sign-In was cancelled', 'error');
                    }
                });
            }

            // Handle Google Response
            async handleGoogleResponse(response) {
                try {
                    this.showToast('Processing Google authentication...', 'info');

                    const result = await fetch(`${AUTH_CONFIG.BACKEND_URL}/google-login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ token: response.credential })
                    });

                    const data = await result.json();

                    if (data.success) {
                        this.handleAuthSuccess(data);
                    } else {
                        throw new Error(data.message || 'Google authentication failed');
                    }
                } catch (error) {
                    console.error('Google login error:', error);
                    this.showToast('Google login failed. Please try again.', 'error');
                } finally {
                    this.showLoading(false, 'google');
                }
            }
        }

        // Initialize authentication system
        let authSystem;
        document.addEventListener('DOMContentLoaded', () => {
            authSystem = new AuthSystem();
        });


    </script>
</body>
</html>
